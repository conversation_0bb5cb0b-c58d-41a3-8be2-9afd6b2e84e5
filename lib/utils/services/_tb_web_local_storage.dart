import 'package:thingsboard_client/thingsboard_client.dart';
import 'package:universal_html/html.dart' as html;

TbStorage createAppStorage() => TbWebLocalStorage();

class TbWebLocalStorage implements TbStorage<String> {
  final html.Storage _localStorage = html.window.localStorage;

  @override
  Future<void> deleteItem(String key) async {
    _localStorage.remove(key);
  }

  @override
  Future<String>? getItem(String key, {String? defaultValue}) {
    return Future(() {
      final value = _localStorage[key];
      if (value != null) {
        return value;
      }
      if (defaultValue != null) {
        return defaultValue;
      }
      return '';
    });
  }

  @override
  Future<void> setItem(String key, String value) async {
    _localStorage[key] = value;
  }

  @override
  Future<bool> containsKey(String key) async {
    return _localStorage.containsKey(key);
  }
}
