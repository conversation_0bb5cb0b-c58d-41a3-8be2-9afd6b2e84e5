{"appTitle": "ThingsBoard", "home": "Home", "alarms": "Alarms", "devices": "Devices", "more": "More", "customers": "Customers", "assets": "Assets", "auditLogs": "<PERSON><PERSON>", "logout": "Log Out", "login": "Log In", "logoDefaultValue": "ThingsBoard Logo", "loginNotification": "Login to your account", "email": "Email", "emailRequireText": "Email is required.", "emailInvalidText": "Invalid email format.", "username": "username", "password": "Password", "passwordRequireText": "Password is required.", "passwordForgotText": "Forgot Password?", "passwordReset": "Reset password", "passwordResetText": "Enter the email associated with your account and we'll send an email with password reset link", "requestPasswordReset": "Request password reset", "passwordResetLinkSuccessfullySentNotification": "Password reset link was successfully sent!", "OR": "OR", "No": "No", "Yes": "Yes", "title": "Title", "country": "Country", "city": "City", "stateOrProvince": "State / Province", "postalCode": "Zip / Postal Code", "address": "Address", "address2": "Address 2", "phone": "Phone", "alarmClearTitle": "Clear Alarm", "alarmClearText": "Are you sure you want to clear Alarm?", "alarmAcknowledgeTitle": "Acknowledge Alarm", "alarmAcknowledgeText": "Are you sure you want to acknowledge <PERSON><PERSON>?", "assetName": "Asset name", "type": "Type", "label": "Label", "assignedToCustomer": "Assigned to customer", "auditLogDetails": "Audit log details", "entityType": "Entity Type", "actionData": "Action data", "failureDetails": "Failure details", "allDevices": "All devices", "active": "Active", "inactive": "Inactive", "systemAdministrator": "System Administrator", "tenantAdministrator": "Tenant Administrator", "customer": "Customer", "changePassword": "Change Password", "currentPassword": "currentPassword", "currentPasswordRequireText": "Current password is required.", "currentPasswordStar": "Current password *", "newPassword": "newPassword", "newPasswordRequireText": "New password is required.", "newPasswordStar": "New password *", "newPassword2": "newPassword2", "newPassword2RequireText": "New password again is required.", "newPassword2Star": "New password again *", "passwordErrorNotification": "Entered passwords must be same!", "emailStar": "Email *", "firstName": "firstName", "firstNameUpper": "First Name", "lastName": "lastName", "lastNameUpper": "Last Name", "profileSuccessNotification": "Profile successfully updated", "passwordSuccessNotification": "Password successfully changed", "notImplemented": "Not implemented!", "listIsEmptyText": "The list is currently empty.", "tryAgain": "Try Again", "verifyYourIdentity": "Verify your identity", "continueText": "Continue", "resendCode": "Resend code", "resendCodeWait": "Resend code in {time,plural, =1{1 second}other{{time} seconds}}", "totpAuthDescription": "Please enter the security code from your authenticator app.", "smsAuthDescription": "A security code has been sent to your phone at {contact}.", "emailAuthDescription": "A security code has been sent to your email address at {contact}.", "backupCodeAuthDescription": "Please enter one of your backup codes.", "verificationCodeInvalid": "Invalid verification code format", "toptAuthPlaceholder": "Code", "smsAuthPlaceholder": "SMS code", "emailAuthPlaceholder": "Email code", "backupCodeAuthPlaceholder": "Backup code", "verificationCodeIncorrect": "Verification code is incorrect", "verificationCodeManyRequest": "Too many requests check verification code", "tryAnotherWay": "Try another way", "selectWayToVerify": "Select a way to verify", "mfaProviderTopt": "Authenticator app", "mfaProviderSms": "SMS", "mfaProviderEmail": "Email", "mfaProviderBackupCode": "Backup code"}