// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a zh locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'zh';

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
        "No": MessageLookupByLibrary.simpleMessage("否"),
        "OR": MessageLookupByLibrary.simpleMessage("或"),
        "Yes": MessageLookupByLibrary.simpleMessage("是"),
        "actionData": MessageLookupByLibrary.simpleMessage("动作数据"),
        "active": MessageLookupByLibrary.simpleMessage("激活"),
        "address": MessageLookupByLibrary.simpleMessage("地址"),
        "address2": MessageLookupByLibrary.simpleMessage("地址 2"),
        "alarmAcknowledgeText":
            MessageLookupByLibrary.simpleMessage("你确定要确认告警吗?"),
        "alarmAcknowledgeTitle": MessageLookupByLibrary.simpleMessage("确认告警"),
        "alarmClearText": MessageLookupByLibrary.simpleMessage("你确定要清除告警吗?"),
        "alarmClearTitle": MessageLookupByLibrary.simpleMessage("清除告警"),
        "alarms": MessageLookupByLibrary.simpleMessage("告警"),
        "allDevices": MessageLookupByLibrary.simpleMessage("所有设备"),
        "appTitle": MessageLookupByLibrary.simpleMessage("Thingsboard"),
        "assetName": MessageLookupByLibrary.simpleMessage("资产名"),
        "assignedToCustomer": MessageLookupByLibrary.simpleMessage("分配给客户"),
        "auditLogDetails": MessageLookupByLibrary.simpleMessage("审计日志详情"),
        "auditLogs": MessageLookupByLibrary.simpleMessage("审计报告"),
        "changePassword": MessageLookupByLibrary.simpleMessage("修改密码"),
        "city": MessageLookupByLibrary.simpleMessage("城市"),
        "country": MessageLookupByLibrary.simpleMessage("国家"),
        "currentPassword": MessageLookupByLibrary.simpleMessage("当前密码"),
        "currentPasswordRequireText":
            MessageLookupByLibrary.simpleMessage("输入当前密码"),
        "currentPasswordStar": MessageLookupByLibrary.simpleMessage("当前密码 *"),
        "customer": MessageLookupByLibrary.simpleMessage("客户"),
        "customers": MessageLookupByLibrary.simpleMessage("客户"),
        "devices": MessageLookupByLibrary.simpleMessage("设备"),
        "email": MessageLookupByLibrary.simpleMessage("Email"),
        "emailInvalidText": MessageLookupByLibrary.simpleMessage("Email格式错误"),
        "emailRequireText": MessageLookupByLibrary.simpleMessage("输入Email"),
        "emailStar": MessageLookupByLibrary.simpleMessage("Email *"),
        "entityType": MessageLookupByLibrary.simpleMessage("实体类型"),
        "failureDetails": MessageLookupByLibrary.simpleMessage("失败详情"),
        "firstName": MessageLookupByLibrary.simpleMessage("名"),
        "firstNameUpper": MessageLookupByLibrary.simpleMessage("名"),
        "home": MessageLookupByLibrary.simpleMessage("主页"),
        "inactive": MessageLookupByLibrary.simpleMessage("失活"),
        "label": MessageLookupByLibrary.simpleMessage("标签"),
        "lastName": MessageLookupByLibrary.simpleMessage("姓"),
        "lastNameUpper": MessageLookupByLibrary.simpleMessage("姓"),
        "listIsEmptyText": MessageLookupByLibrary.simpleMessage("列表当前为空"),
        "login": MessageLookupByLibrary.simpleMessage("登录"),
        "loginNotification": MessageLookupByLibrary.simpleMessage("登录你的账号"),
        "logoDefaultValue":
            MessageLookupByLibrary.simpleMessage("Thingsboard Logo"),
        "logout": MessageLookupByLibrary.simpleMessage("登出"),
        "more": MessageLookupByLibrary.simpleMessage("更多"),
        "newPassword": MessageLookupByLibrary.simpleMessage("新密码"),
        "newPassword2": MessageLookupByLibrary.simpleMessage("新密码2"),
        "newPassword2RequireText":
            MessageLookupByLibrary.simpleMessage("再次输入新密码"),
        "newPassword2Star": MessageLookupByLibrary.simpleMessage("再次输入新密码 *"),
        "newPasswordRequireText": MessageLookupByLibrary.simpleMessage("输入新密码"),
        "newPasswordStar": MessageLookupByLibrary.simpleMessage("新密码 *"),
        "notImplemented": MessageLookupByLibrary.simpleMessage("未实现!"),
        "password": MessageLookupByLibrary.simpleMessage("密码"),
        "passwordErrorNotification":
            MessageLookupByLibrary.simpleMessage("输入的密码必须相同"),
        "passwordForgotText": MessageLookupByLibrary.simpleMessage("忘记密码?"),
        "passwordRequireText": MessageLookupByLibrary.simpleMessage("输入密码"),
        "passwordReset": MessageLookupByLibrary.simpleMessage("重置密码"),
        "passwordResetLinkSuccessfullySentNotification":
            MessageLookupByLibrary.simpleMessage("密码重置链接已发送"),
        "passwordResetText": MessageLookupByLibrary.simpleMessage(
            "输入和账号关联的Email，我们将发送一个密码重置链接到的Email"),
        "passwordSuccessNotification":
            MessageLookupByLibrary.simpleMessage("密码修改成功"),
        "phone": MessageLookupByLibrary.simpleMessage("电话"),
        "postalCode": MessageLookupByLibrary.simpleMessage("邮编"),
        "profileSuccessNotification":
            MessageLookupByLibrary.simpleMessage("配置更新成功"),
        "requestPasswordReset": MessageLookupByLibrary.simpleMessage("要求重置密码"),
        "stateOrProvince": MessageLookupByLibrary.simpleMessage("州 / 省"),
        "systemAdministrator": MessageLookupByLibrary.simpleMessage("系统管理员"),
        "tenantAdministrator": MessageLookupByLibrary.simpleMessage("租户管理员"),
        "title": MessageLookupByLibrary.simpleMessage("标题"),
        "tryAgain": MessageLookupByLibrary.simpleMessage("再试一次"),
        "type": MessageLookupByLibrary.simpleMessage("类型"),
        "username": MessageLookupByLibrary.simpleMessage("用户名")
      };
}
