import 'package:flutter/material.dart';
// import 'package:flutter_slidable/flutter_slidable.dart';  // Temporarily disabled
import 'package:thingsboard_app/core/context/tb_context.dart';
import 'package:thingsboard_app/generated/l10n.dart';
import 'package:thingsboard_client/thingsboard_client.dart';

class NotificationSlidableWidget extends StatefulWidget {
  const NotificationSlidableWidget({
    required this.child,
    required this.notification,
    required this.thingsboardClient,
    required this.onClearNotification,
    required this.onReadNotification,
    required this.tbContext,
  });

  final Widget child;
  final PushNotification notification;
  final ThingsboardClient thingsboardClient;
  final Function(String id, bool read) onClearNotification;
  final ValueChanged<String> onReadNotification;
  final TbContext tbContext;

  @override
  State<StatefulWidget> createState() => _NotificationSlidableWidget();
}

class _NotificationSlidableWidget extends State<NotificationSlidableWidget> {
  bool loading = false;

  @override
  Widget build(BuildContext context) {
    if (loading) {
      return Container(
        height: 134,
        alignment: Alignment.center,
        child: RefreshProgressIndicator(),
      );
    }

    // Temporarily disabled slidable functionality due to compatibility issues
    return Container(
      key: ValueKey(widget.notification.id!.id),
      child: widget.child,
    );
  }

  List<Widget> _buildAlarmRelatedButtons(PushNotification notification) {
    final items = <Widget>[];

    final type = notification.type;
    if (type == PushNotificationType.ALARM) {
      final status = notification.info?.alarmStatus;
      final id = notification.info?.alarmId;

      if (id != null) {
        if ([AlarmStatus.CLEARED_UNACK, AlarmStatus.ACTIVE_UNACK]
            .contains(status)) {
          // Alarm acknowledgment functionality temporarily disabled
        }

        if ([AlarmStatus.ACTIVE_UNACK, AlarmStatus.ACTIVE_ACK]
            .contains(status)) {
          // Alarm clear functionality temporarily disabled
        }
      }
    }

    return items;
  }

  void _ackAlarm(String alarmId, BuildContext context) async {
    final res = await widget.tbContext.confirm(
        title: '${S.of(context).alarmAcknowledgeTitle}',
        message: '${S.of(context).alarmAcknowledgeText}',
        cancel: '${S.of(context).No}',
        ok: '${S.of(context).Yes}');

    if (res != null && res) {
      setState(() {
        loading = true;
      });
      try {
        await widget.thingsboardClient.getAlarmService().ackAlarm(alarmId);
      } catch (_) {}

      setState(() {
        loading = false;
        widget.onReadNotification(widget.notification.id!.id!);
      });
    }
  }

  void _clearAlarm(String alarmId, BuildContext context) async {
    final res = await widget.tbContext.confirm(
        title: '${S.of(context).alarmClearTitle}',
        message: '${S.of(context).alarmClearText}',
        cancel: '${S.of(context).No}',
        ok: '${S.of(context).Yes}');
    if (res != null && res) {
      setState(() {
        loading = true;
      });

      try {
        await widget.thingsboardClient.getAlarmService().clearAlarm(alarmId);
      } catch (_) {}

      setState(() {
        loading = false;
        widget.onReadNotification(widget.notification.id!.id!);
      });
    }
  }

  double _endExtentRatio(PushNotification notification) {
    final items = _buildAlarmRelatedButtons(notification);
    if (items.isEmpty) {
      return 0.27;
    } else if (items.length == 1) {
      return 0.53;
    }

    return 0.8;
  }
}
