import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:material_design_icons_flutter/material_design_icons_flutter.dart';
import 'package:thingsboard_app/constants/app_constants.dart';
import 'package:thingsboard_client/thingsboard_client.dart';

class NotificationIcon extends StatelessWidget {
  const NotificationIcon({required this.notification});

  final PushNotification notification;

  @override
  Widget build(BuildContext context) {
    final iconData = _toIcon(notification.additionalConfig?['icon'] ?? {});

    return iconData;
  }

  Color _toColor(String? data) {
    if (data != null) {
      var hexColor = data.replaceAll("#", "");
      if (hexColor.length == 6) {
        hexColor = "FF" + hexColor;
      }

      if (hexColor.length == 8) {
        final value = int.tryParse('0x$hexColor');
        if (value != null) {
          return Color(value);
        }
      }
    }

    return Colors.black54;
  }

  Widget _toIcon(Map<String, dynamic> data) {
    final imageData = data['icon'];

    if (imageData != null) {
      if (imageData!.contains('mdi')) {
        return Icon(
          MdiIcons.fromString(imageData.split('mdi:').last),
          color: _toColor(data['color']),
        );

        return SvgPicture.network(
          '${ThingsboardAppConstants.thingsBoardApiEndpoint}/assets/mdi/${imageData.split('mdi:').last}.svg',
          color: _toColor(data['color']),
        );
      }

      return Icon(
        materialIconsMap[imageData],
        color: _toColor(
          data['color'],
        ),
      );
    }

    return Icon(Icons.notifications, color: Colors.black54);
  }
}

const materialIconsMap = {
  '10k': IconData(0xe000, fontFamily: 'MaterialIcons'),
  '10mp': IconData(0xe001, fontFamily: 'MaterialIcons'),
  '11mp': IconData(0xe002, fontFamily: 'MaterialIcons'),
  '123': IconData(0xf04b5, fontFamily: 'MaterialIcons'),
  '12mp': IconData(0xe003, fontFamily: 'MaterialIcons'),
  '13mp': IconData(0xe004, fontFamily: 'MaterialIcons'),
  '14mp': IconData(0xe005, fontFamily: 'MaterialIcons'),
  '15mp': IconData(0xe006, fontFamily: 'MaterialIcons'),
  '16mp': IconData(0xe007, fontFamily: 'MaterialIcons'),
  '17mp': IconData(0xe008, fontFamily: 'MaterialIcons'),
  '18_up_rating': IconData(0xf0784, fontFamily: 'MaterialIcons'),
  '18mp': IconData(0xe009, fontFamily: 'MaterialIcons'),
  '19mp': IconData(0xe00a, fontFamily: 'MaterialIcons'),
  '1k': IconData(0xe00b, fontFamily: 'MaterialIcons'),
  '1k_plus': IconData(0xe00c, fontFamily: 'MaterialIcons'),
  '1x_mobiledata': IconData(0xe00d, fontFamily: 'MaterialIcons'),
  '20mp': IconData(0xe00e, fontFamily: 'MaterialIcons'),
  '21mp': IconData(0xe00f, fontFamily: 'MaterialIcons'),
  '22mp': IconData(0xe010, fontFamily: 'MaterialIcons'),
  '23mp': IconData(0xe011, fontFamily: 'MaterialIcons'),
  '24mp': IconData(0xe012, fontFamily: 'MaterialIcons'),
  '2k': IconData(0xe013, fontFamily: 'MaterialIcons'),
  '2k_plus': IconData(0xe014, fontFamily: 'MaterialIcons'),
  '2mp': IconData(0xe015, fontFamily: 'MaterialIcons'),
  '30fps': IconData(0xe016, fontFamily: 'MaterialIcons'),
  '30fps_select': IconData(0xe017, fontFamily: 'MaterialIcons'),
  '360': IconData(0xe018, fontFamily: 'MaterialIcons'),
  '3d_rotation': IconData(0xe019, fontFamily: 'MaterialIcons'),
  '3g_mobiledata': IconData(0xe01a, fontFamily: 'MaterialIcons'),
  '3k': IconData(0xe01b, fontFamily: 'MaterialIcons'),
  '3k_plus': IconData(0xe01c, fontFamily: 'MaterialIcons'),
  '3mp': IconData(0xe01d, fontFamily: 'MaterialIcons'),
  '3p': IconData(0xe01e, fontFamily: 'MaterialIcons'),
  '4g_mobiledata': IconData(0xe01f, fontFamily: 'MaterialIcons'),
  '4g_plus_mobiledata': IconData(0xe020, fontFamily: 'MaterialIcons'),
  '4k': IconData(0xe021, fontFamily: 'MaterialIcons'),
  '4k_plus': IconData(0xe022, fontFamily: 'MaterialIcons'),
  '4mp': IconData(0xe023, fontFamily: 'MaterialIcons'),
  '5g': IconData(0xe024, fontFamily: 'MaterialIcons'),
  '5k': IconData(0xe025, fontFamily: 'MaterialIcons'),
  '5k_plus': IconData(0xe026, fontFamily: 'MaterialIcons'),
  '5mp': IconData(0xe027, fontFamily: 'MaterialIcons'),
  '60fps': IconData(0xe028, fontFamily: 'MaterialIcons'),
  '60fps_select': IconData(0xe029, fontFamily: 'MaterialIcons'),
  '6_ft_apart': IconData(0xe02a, fontFamily: 'MaterialIcons'),
  '6k': IconData(0xe02b, fontFamily: 'MaterialIcons'),
  '6k_plus': IconData(0xe02c, fontFamily: 'MaterialIcons'),
  '6mp': IconData(0xe02d, fontFamily: 'MaterialIcons'),
  '7k': IconData(0xe02e, fontFamily: 'MaterialIcons'),
  '7k_plus': IconData(0xe02f, fontFamily: 'MaterialIcons'),
  '7mp': IconData(0xe030, fontFamily: 'MaterialIcons'),
  '8k': IconData(0xe031, fontFamily: 'MaterialIcons'),
  '8k_plus': IconData(0xe032, fontFamily: 'MaterialIcons'),
  '8mp': IconData(0xe033, fontFamily: 'MaterialIcons'),
  '9k': IconData(0xe034, fontFamily: 'MaterialIcons'),
  '9k_plus': IconData(0xe035, fontFamily: 'MaterialIcons'),
  '9mp': IconData(0xe036, fontFamily: 'MaterialIcons'),
  'abc': IconData(0xf04b6, fontFamily: 'MaterialIcons'),
  'ac_unit': IconData(0xe037, fontFamily: 'MaterialIcons'),
  'access_alarm': IconData(0xe038, fontFamily: 'MaterialIcons'),
  'access_alarms': IconData(0xe039, fontFamily: 'MaterialIcons'),
  'access_time': IconData(0xe03a, fontFamily: 'MaterialIcons'),
  'access_time_filled': IconData(0xe03b, fontFamily: 'MaterialIcons'),
  'accessibility': IconData(0xe03c, fontFamily: 'MaterialIcons'),
  'accessibility_new': IconData(0xe03d, fontFamily: 'MaterialIcons'),
  'accessible': IconData(0xe03e, fontFamily: 'MaterialIcons'),
  'accessible_forward': IconData(0xe03f, fontFamily: 'MaterialIcons'),
  'account_balance': IconData(0xe040, fontFamily: 'MaterialIcons'),
  'account_balance_wallet': IconData(0xe041, fontFamily: 'MaterialIcons'),
  'account_box': IconData(0xe042, fontFamily: 'MaterialIcons'),
  'account_circle': IconData(0xe043, fontFamily: 'MaterialIcons'),
  'account_tree': IconData(0xe044, fontFamily: 'MaterialIcons'),
  'ad_units': IconData(0xe045, fontFamily: 'MaterialIcons'),
  'adb': IconData(0xe046, fontFamily: 'MaterialIcons'),
  'add': IconData(0xe047, fontFamily: 'MaterialIcons'),
  'add_a_photo': IconData(0xe048, fontFamily: 'MaterialIcons'),
  'add_alarm': IconData(0xe049, fontFamily: 'MaterialIcons'),
  'add_alert': IconData(0xe04a, fontFamily: 'MaterialIcons'),
  'add_box': IconData(0xe04b, fontFamily: 'MaterialIcons'),
  'add_business': IconData(0xe04c, fontFamily: 'MaterialIcons'),
  'add_call': IconData(0xe04d, fontFamily: 'MaterialIcons'),
  'add_card': IconData(0xf04b7, fontFamily: 'MaterialIcons'),
  'add_chart': IconData(0xe04e, fontFamily: 'MaterialIcons'),
  'add_circle': IconData(0xe04f, fontFamily: 'MaterialIcons'),
  'add_circle_outline': IconData(0xe050, fontFamily: 'MaterialIcons'),
  'add_comment': IconData(0xe051, fontFamily: 'MaterialIcons'),
  'add_home': IconData(0xf0785, fontFamily: 'MaterialIcons'),
  'add_home_work': IconData(0xf0786, fontFamily: 'MaterialIcons'),
  'add_ic_call': IconData(0xe052, fontFamily: 'MaterialIcons'),
  'add_link': IconData(0xe053, fontFamily: 'MaterialIcons'),
  'add_location': IconData(0xe054, fontFamily: 'MaterialIcons'),
  'add_location_alt': IconData(0xe055, fontFamily: 'MaterialIcons'),
  'add_moderator': IconData(0xe056, fontFamily: 'MaterialIcons'),
  'add_photo_alternate': IconData(0xe057, fontFamily: 'MaterialIcons'),
  'add_reaction': IconData(0xe058, fontFamily: 'MaterialIcons'),
  'add_road': IconData(0xe059, fontFamily: 'MaterialIcons'),
  'add_shopping_cart': IconData(0xe05a, fontFamily: 'MaterialIcons'),
  'add_task': IconData(0xe05b, fontFamily: 'MaterialIcons'),
  'add_to_drive': IconData(0xe05c, fontFamily: 'MaterialIcons'),
  'add_to_home_screen': IconData(0xe05d, fontFamily: 'MaterialIcons'),
  'add_to_photos': IconData(0xe05e, fontFamily: 'MaterialIcons'),
  'add_to_queue': IconData(0xe05f, fontFamily: 'MaterialIcons'),
  'addchart': IconData(0xe060, fontFamily: 'MaterialIcons'),
  'adf_scanner': IconData(0xf04b8, fontFamily: 'MaterialIcons'),
  'adjust': IconData(0xe061, fontFamily: 'MaterialIcons'),
  'admin_panel_settings': IconData(0xe062, fontFamily: 'MaterialIcons'),
  'adobe': IconData(0xf04b9, fontFamily: 'MaterialIcons'),
  'ads_click': IconData(0xf04ba, fontFamily: 'MaterialIcons'),
  'agriculture': IconData(0xe063, fontFamily: 'MaterialIcons'),
  'air': IconData(0xe064, fontFamily: 'MaterialIcons'),
  'airline_seat_flat': IconData(0xe065, fontFamily: 'MaterialIcons'),
  'airline_seat_flat_angled': IconData(0xe066, fontFamily: 'MaterialIcons'),
  'airline_seat_individual_suite':
      IconData(0xe067, fontFamily: 'MaterialIcons'),
  'airline_seat_legroom_extra': IconData(0xe068, fontFamily: 'MaterialIcons'),
  'airline_seat_legroom_normal': IconData(0xe069, fontFamily: 'MaterialIcons'),
  'airline_seat_legroom_reduced': IconData(0xe06a, fontFamily: 'MaterialIcons'),
  'airline_seat_recline_extra': IconData(0xe06b, fontFamily: 'MaterialIcons'),
  'airline_seat_recline_normal': IconData(0xe06c, fontFamily: 'MaterialIcons'),
  'airline_stops': IconData(0xf04bb, fontFamily: 'MaterialIcons'),
  'airlines': IconData(0xf04bc, fontFamily: 'MaterialIcons'),
  'airplane_ticket': IconData(0xe06d, fontFamily: 'MaterialIcons'),
  'airplanemode_active': IconData(0xe06e, fontFamily: 'MaterialIcons'),
  'airplanemode_inactive': IconData(0xe06f, fontFamily: 'MaterialIcons'),
  'airplanemode_off': IconData(0xe06f, fontFamily: 'MaterialIcons'),
  'airplanemode_on': IconData(0xe06e, fontFamily: 'MaterialIcons'),
  'airplay': IconData(0xe070, fontFamily: 'MaterialIcons'),
  'airport_shuttle': IconData(0xe071, fontFamily: 'MaterialIcons'),
  'alarm': IconData(0xe072, fontFamily: 'MaterialIcons'),
  'alarm_add': IconData(0xe073, fontFamily: 'MaterialIcons'),
  'alarm_off': IconData(0xe074, fontFamily: 'MaterialIcons'),
  'alarm_on': IconData(0xe075, fontFamily: 'MaterialIcons'),
  'album': IconData(0xe076, fontFamily: 'MaterialIcons'),
  'align_horizontal_center': IconData(0xe077, fontFamily: 'MaterialIcons'),
  'align_horizontal_left': IconData(0xe078, fontFamily: 'MaterialIcons'),
  'align_horizontal_right': IconData(0xe079, fontFamily: 'MaterialIcons'),
  'align_vertical_bottom': IconData(0xe07a, fontFamily: 'MaterialIcons'),
  'align_vertical_center': IconData(0xe07b, fontFamily: 'MaterialIcons'),
  'align_vertical_top': IconData(0xe07c, fontFamily: 'MaterialIcons'),
  'all_inbox': IconData(0xe07d, fontFamily: 'MaterialIcons'),
  'all_inclusive': IconData(0xe07e, fontFamily: 'MaterialIcons'),
  'all_out': IconData(0xe07f, fontFamily: 'MaterialIcons'),
  'alt_route': IconData(0xe080, fontFamily: 'MaterialIcons'),
  'alternate_email': IconData(0xe081, fontFamily: 'MaterialIcons'),
  'amp_stories': IconData(0xe082, fontFamily: 'MaterialIcons'),
  'analytics': IconData(0xe083, fontFamily: 'MaterialIcons'),
  'anchor': IconData(0xe084, fontFamily: 'MaterialIcons'),
  'android': IconData(0xe085, fontFamily: 'MaterialIcons'),
  'animation': IconData(0xe086, fontFamily: 'MaterialIcons'),
  'announcement': IconData(0xe087, fontFamily: 'MaterialIcons'),
  'aod': IconData(0xe088, fontFamily: 'MaterialIcons'),
  'apartment': IconData(0xe089, fontFamily: 'MaterialIcons'),
  'api': IconData(0xe08a, fontFamily: 'MaterialIcons'),
  'app_blocking': IconData(0xe08b, fontFamily: 'MaterialIcons'),
  'app_registration': IconData(0xe08c, fontFamily: 'MaterialIcons'),
  'app_settings_alt': IconData(0xe08d, fontFamily: 'MaterialIcons'),
  'app_shortcut': IconData(0xf04bd, fontFamily: 'MaterialIcons'),
  'apple': IconData(0xf04be, fontFamily: 'MaterialIcons'),
  'approval': IconData(0xe08e, fontFamily: 'MaterialIcons'),
  'apps': IconData(0xe08f, fontFamily: 'MaterialIcons'),
  'apps_outage': IconData(0xf04bf, fontFamily: 'MaterialIcons'),
  'architecture': IconData(0xe090, fontFamily: 'MaterialIcons'),
  'archive': IconData(0xe091, fontFamily: 'MaterialIcons'),
  'area_chart': IconData(0xf04c0, fontFamily: 'MaterialIcons'),
  'arrow_back':
      IconData(0xe092, fontFamily: 'MaterialIcons', matchTextDirection: true),
  'arrow_back_ios':
      IconData(0xe093, fontFamily: 'MaterialIcons', matchTextDirection: true),
  'arrow_back_ios_new':
      IconData(0xe094, fontFamily: 'MaterialIcons', matchTextDirection: true),
  'arrow_circle_down': IconData(0xe095, fontFamily: 'MaterialIcons'),
  'arrow_circle_left': IconData(0xf04c1, fontFamily: 'MaterialIcons'),
  'arrow_circle_right': IconData(0xf04c2, fontFamily: 'MaterialIcons'),
  'arrow_circle_up': IconData(0xe096, fontFamily: 'MaterialIcons'),
  'arrow_downward': IconData(0xe097, fontFamily: 'MaterialIcons'),
  'arrow_drop_down': IconData(0xe098, fontFamily: 'MaterialIcons'),
  'arrow_drop_down_circle': IconData(0xe099, fontFamily: 'MaterialIcons'),
  'arrow_drop_up': IconData(0xe09a, fontFamily: 'MaterialIcons'),
  'arrow_forward':
      IconData(0xe09b, fontFamily: 'MaterialIcons', matchTextDirection: true),
  'arrow_forward_ios':
      IconData(0xe09c, fontFamily: 'MaterialIcons', matchTextDirection: true),
  'arrow_left':
      IconData(0xe09d, fontFamily: 'MaterialIcons', matchTextDirection: true),
  'arrow_outward': IconData(0xf0852, fontFamily: 'MaterialIcons'),
  'arrow_right':
      IconData(0xe09e, fontFamily: 'MaterialIcons', matchTextDirection: true),
  'arrow_right_alt':
      IconData(0xe09f, fontFamily: 'MaterialIcons', matchTextDirection: true),
  'arrow_upward': IconData(0xe0a0, fontFamily: 'MaterialIcons'),
  'art_track': IconData(0xe0a1, fontFamily: 'MaterialIcons'),
  'article': IconData(0xe0a2, fontFamily: 'MaterialIcons'),
  'aspect_ratio': IconData(0xe0a3, fontFamily: 'MaterialIcons'),
  'assessment': IconData(0xe0a4, fontFamily: 'MaterialIcons'),
  'assignment':
      IconData(0xe0a5, fontFamily: 'MaterialIcons', matchTextDirection: true),
  'assignment_add': IconData(0xf0853, fontFamily: 'MaterialIcons'),
  'assignment_ind': IconData(0xe0a6, fontFamily: 'MaterialIcons'),
  'assignment_late': IconData(0xe0a7, fontFamily: 'MaterialIcons'),
  'assignment_return':
      IconData(0xe0a8, fontFamily: 'MaterialIcons', matchTextDirection: true),
  'assignment_returned': IconData(0xe0a9, fontFamily: 'MaterialIcons'),
  'assignment_turned_in': IconData(0xe0aa, fontFamily: 'MaterialIcons'),
  'assist_walker': IconData(0xf0854, fontFamily: 'MaterialIcons'),
  'assistant': IconData(0xe0ab, fontFamily: 'MaterialIcons'),
  'assistant_direction': IconData(0xe0ac, fontFamily: 'MaterialIcons'),
  'assistant_navigation': IconData(0xe0ad, fontFamily: 'MaterialIcons'),
  'assistant_photo': IconData(0xe0ae, fontFamily: 'MaterialIcons'),
  'assured_workload': IconData(0xf04c3, fontFamily: 'MaterialIcons'),
  'atm': IconData(0xe0af, fontFamily: 'MaterialIcons'),
  'attach_email': IconData(0xe0b0, fontFamily: 'MaterialIcons'),
  'attach_file': IconData(0xe0b1, fontFamily: 'MaterialIcons'),
  'attach_money': IconData(0xe0b2, fontFamily: 'MaterialIcons'),
  'attachment': IconData(0xe0b3, fontFamily: 'MaterialIcons'),
  'attractions': IconData(0xe0b4, fontFamily: 'MaterialIcons'),
  'attribution': IconData(0xe0b5, fontFamily: 'MaterialIcons'),
  'audio_file': IconData(0xf04c4, fontFamily: 'MaterialIcons'),
  'audiotrack': IconData(0xe0b6, fontFamily: 'MaterialIcons'),
  'auto_awesome': IconData(0xe0b7, fontFamily: 'MaterialIcons'),
  'auto_awesome_mosaic': IconData(0xe0b8, fontFamily: 'MaterialIcons'),
  'auto_awesome_motion': IconData(0xe0b9, fontFamily: 'MaterialIcons'),
  'auto_delete': IconData(0xe0ba, fontFamily: 'MaterialIcons'),
  'auto_fix_high': IconData(0xe0bb, fontFamily: 'MaterialIcons'),
  'auto_fix_normal': IconData(0xe0bc, fontFamily: 'MaterialIcons'),
  'auto_fix_off': IconData(0xe0bd, fontFamily: 'MaterialIcons'),
  'auto_graph': IconData(0xe0be, fontFamily: 'MaterialIcons'),
  'auto_mode': IconData(0xf0787, fontFamily: 'MaterialIcons'),
  'auto_stories': IconData(0xe0bf, fontFamily: 'MaterialIcons'),
  'autofps_select': IconData(0xe0c0, fontFamily: 'MaterialIcons'),
  'autorenew': IconData(0xe0c1, fontFamily: 'MaterialIcons'),
  'av_timer': IconData(0xe0c2, fontFamily: 'MaterialIcons'),
  'baby_changing_station': IconData(0xe0c3, fontFamily: 'MaterialIcons'),
  'back_hand': IconData(0xf04c5, fontFamily: 'MaterialIcons'),
  'backpack': IconData(0xe0c4, fontFamily: 'MaterialIcons'),
  'backspace':
      IconData(0xe0c5, fontFamily: 'MaterialIcons', matchTextDirection: true),
  'backup': IconData(0xe0c6, fontFamily: 'MaterialIcons'),
  'backup_table': IconData(0xe0c7, fontFamily: 'MaterialIcons'),
  'badge': IconData(0xe0c8, fontFamily: 'MaterialIcons'),
  'bakery_dining': IconData(0xe0c9, fontFamily: 'MaterialIcons'),
  'balance': IconData(0xf04c6, fontFamily: 'MaterialIcons'),
  'balcony': IconData(0xe0ca, fontFamily: 'MaterialIcons'),
  'ballot': IconData(0xe0cb, fontFamily: 'MaterialIcons'),
  'bar_chart': IconData(0xe0cc, fontFamily: 'MaterialIcons'),
  'barcode_reader': IconData(0xf0855, fontFamily: 'MaterialIcons'),
  'batch_prediction': IconData(0xe0cd, fontFamily: 'MaterialIcons'),
  'bathroom': IconData(0xe0ce, fontFamily: 'MaterialIcons'),
  'bathtub': IconData(0xe0cf, fontFamily: 'MaterialIcons'),
  'battery_0_bar': IconData(0xf0788, fontFamily: 'MaterialIcons'),
  'battery_1_bar': IconData(0xf0789, fontFamily: 'MaterialIcons'),
  'battery_2_bar': IconData(0xf078a, fontFamily: 'MaterialIcons'),
  'battery_3_bar': IconData(0xf078b, fontFamily: 'MaterialIcons'),
  'battery_4_bar': IconData(0xf078c, fontFamily: 'MaterialIcons'),
  'battery_5_bar': IconData(0xf078d, fontFamily: 'MaterialIcons'),
  'battery_6_bar': IconData(0xf078e, fontFamily: 'MaterialIcons'),
  'battery_alert': IconData(0xe0d0, fontFamily: 'MaterialIcons'),
  'battery_charging_full': IconData(0xe0d1, fontFamily: 'MaterialIcons'),
  'battery_full': IconData(0xe0d2, fontFamily: 'MaterialIcons'),
  'battery_saver': IconData(0xe0d3, fontFamily: 'MaterialIcons'),
  'battery_std': IconData(0xe0d4, fontFamily: 'MaterialIcons'),
  'battery_unknown':
      IconData(0xe0d5, fontFamily: 'MaterialIcons', matchTextDirection: true),
  'beach_access': IconData(0xe0d6, fontFamily: 'MaterialIcons'),
  'bed': IconData(0xe0d7, fontFamily: 'MaterialIcons'),
  'bedroom_baby': IconData(0xe0d8, fontFamily: 'MaterialIcons'),
  'bedroom_child': IconData(0xe0d9, fontFamily: 'MaterialIcons'),
  'bedroom_parent': IconData(0xe0da, fontFamily: 'MaterialIcons'),
  'bedtime': IconData(0xe0db, fontFamily: 'MaterialIcons'),
  'bedtime_off': IconData(0xf04c7, fontFamily: 'MaterialIcons'),
  'beenhere': IconData(0xe0dc, fontFamily: 'MaterialIcons'),
  'bento': IconData(0xe0dd, fontFamily: 'MaterialIcons'),
  'bike_scooter': IconData(0xe0de, fontFamily: 'MaterialIcons'),
  'biotech': IconData(0xe0df, fontFamily: 'MaterialIcons'),
  'blender': IconData(0xe0e0, fontFamily: 'MaterialIcons'),
  'blind': IconData(0xf0856, fontFamily: 'MaterialIcons'),
  'blinds': IconData(0xf078f, fontFamily: 'MaterialIcons'),
  'blinds_closed': IconData(0xf0790, fontFamily: 'MaterialIcons'),
  'block': IconData(0xe0e1, fontFamily: 'MaterialIcons'),
  'block_flipped': IconData(0xe0e2, fontFamily: 'MaterialIcons'),
  'bloodtype': IconData(0xe0e3, fontFamily: 'MaterialIcons'),
  'bluetooth': IconData(0xe0e4, fontFamily: 'MaterialIcons'),
  'bluetooth_audio': IconData(0xe0e5, fontFamily: 'MaterialIcons'),
  'bluetooth_connected': IconData(0xe0e6, fontFamily: 'MaterialIcons'),
  'bluetooth_disabled': IconData(0xe0e7, fontFamily: 'MaterialIcons'),
  'bluetooth_drive': IconData(0xe0e8, fontFamily: 'MaterialIcons'),
  'bluetooth_searching': IconData(0xe0e9, fontFamily: 'MaterialIcons'),
  'blur_circular': IconData(0xe0ea, fontFamily: 'MaterialIcons'),
  'blur_linear': IconData(0xe0eb, fontFamily: 'MaterialIcons'),
  'blur_off': IconData(0xe0ec, fontFamily: 'MaterialIcons'),
  'blur_on': IconData(0xe0ed, fontFamily: 'MaterialIcons'),
  'bolt': IconData(0xe0ee, fontFamily: 'MaterialIcons'),
  'book': IconData(0xe0ef, fontFamily: 'MaterialIcons'),
  'book_online': IconData(0xe0f0, fontFamily: 'MaterialIcons'),
  'bookmark': IconData(0xe0f1, fontFamily: 'MaterialIcons'),
  'bookmark_add': IconData(0xe0f2, fontFamily: 'MaterialIcons'),
  'bookmark_added': IconData(0xe0f3, fontFamily: 'MaterialIcons'),
  'bookmark_border': IconData(0xe0f4, fontFamily: 'MaterialIcons'),
  'bookmark_outline': IconData(0xe0f4, fontFamily: 'MaterialIcons'),
  'bookmark_remove': IconData(0xe0f5, fontFamily: 'MaterialIcons'),
  'bookmarks': IconData(0xe0f6, fontFamily: 'MaterialIcons'),
  'border_all': IconData(0xe0f7, fontFamily: 'MaterialIcons'),
  'border_bottom': IconData(0xe0f8, fontFamily: 'MaterialIcons'),
  'border_clear': IconData(0xe0f9, fontFamily: 'MaterialIcons'),
  'border_color': IconData(0xe0fa, fontFamily: 'MaterialIcons'),
  'border_horizontal': IconData(0xe0fb, fontFamily: 'MaterialIcons'),
  'border_inner': IconData(0xe0fc, fontFamily: 'MaterialIcons'),
  'border_left': IconData(0xe0fd, fontFamily: 'MaterialIcons'),
  'border_outer': IconData(0xe0fe, fontFamily: 'MaterialIcons'),
  'border_right': IconData(0xe0ff, fontFamily: 'MaterialIcons'),
  'border_style': IconData(0xe100, fontFamily: 'MaterialIcons'),
  'border_top': IconData(0xe101, fontFamily: 'MaterialIcons'),
  'border_vertical': IconData(0xe102, fontFamily: 'MaterialIcons'),
  'boy': IconData(0xf04c8, fontFamily: 'MaterialIcons'),
  'branding_watermark': IconData(0xe103, fontFamily: 'MaterialIcons'),
  'breakfast_dining': IconData(0xe104, fontFamily: 'MaterialIcons'),
  'brightness_1': IconData(0xe105, fontFamily: 'MaterialIcons'),
  'brightness_2': IconData(0xe106, fontFamily: 'MaterialIcons'),
  'brightness_3': IconData(0xe107, fontFamily: 'MaterialIcons'),
  'brightness_4': IconData(0xe108, fontFamily: 'MaterialIcons'),
  'brightness_5': IconData(0xe109, fontFamily: 'MaterialIcons'),
  'brightness_6': IconData(0xe10a, fontFamily: 'MaterialIcons'),
  'brightness_7': IconData(0xe10b, fontFamily: 'MaterialIcons'),
  'brightness_auto': IconData(0xe10c, fontFamily: 'MaterialIcons'),
  'brightness_high': IconData(0xe10d, fontFamily: 'MaterialIcons'),
  'brightness_low': IconData(0xe10e, fontFamily: 'MaterialIcons'),
  'brightness_medium': IconData(0xe10f, fontFamily: 'MaterialIcons'),
  'broadcast_on_home': IconData(0xf0791, fontFamily: 'MaterialIcons'),
  'broadcast_on_personal': IconData(0xf0792, fontFamily: 'MaterialIcons'),
  'broken_image': IconData(0xe110, fontFamily: 'MaterialIcons'),
  'browse_gallery': IconData(0xf06ba, fontFamily: 'MaterialIcons'),
  'browser_not_supported': IconData(0xe111, fontFamily: 'MaterialIcons'),
  'browser_updated': IconData(0xf04c9, fontFamily: 'MaterialIcons'),
  'brunch_dining': IconData(0xe112, fontFamily: 'MaterialIcons'),
  'brush': IconData(0xe113, fontFamily: 'MaterialIcons'),
  'bubble_chart': IconData(0xe114, fontFamily: 'MaterialIcons'),
  'bug_report': IconData(0xe115, fontFamily: 'MaterialIcons'),
  'build': IconData(0xe116, fontFamily: 'MaterialIcons'),
  'build_circle': IconData(0xe117, fontFamily: 'MaterialIcons'),
  'bungalow': IconData(0xe118, fontFamily: 'MaterialIcons'),
  'burst_mode': IconData(0xe119, fontFamily: 'MaterialIcons'),
  'bus_alert': IconData(0xe11a, fontFamily: 'MaterialIcons'),
  'business': IconData(0xe11b, fontFamily: 'MaterialIcons'),
  'business_center': IconData(0xe11c, fontFamily: 'MaterialIcons'),
  'cabin': IconData(0xe11d, fontFamily: 'MaterialIcons'),
  'cable': IconData(0xe11e, fontFamily: 'MaterialIcons'),
  'cached': IconData(0xe11f, fontFamily: 'MaterialIcons'),
  'cake': IconData(0xe120, fontFamily: 'MaterialIcons'),
  'calculate': IconData(0xe121, fontFamily: 'MaterialIcons'),
  'calendar_month': IconData(0xf06bb, fontFamily: 'MaterialIcons'),
  'calendar_today': IconData(0xe122, fontFamily: 'MaterialIcons'),
  'calendar_view_day': IconData(0xe123, fontFamily: 'MaterialIcons'),
  'calendar_view_month': IconData(0xe124, fontFamily: 'MaterialIcons'),
  'calendar_view_week': IconData(0xe125, fontFamily: 'MaterialIcons'),
  'call': IconData(0xe126, fontFamily: 'MaterialIcons'),
  'call_end': IconData(0xe127, fontFamily: 'MaterialIcons'),
  'call_made':
      IconData(0xe128, fontFamily: 'MaterialIcons', matchTextDirection: true),
  'call_merge':
      IconData(0xe129, fontFamily: 'MaterialIcons', matchTextDirection: true),
  'call_missed':
      IconData(0xe12a, fontFamily: 'MaterialIcons', matchTextDirection: true),
  'call_missed_outgoing':
      IconData(0xe12b, fontFamily: 'MaterialIcons', matchTextDirection: true),
  'call_received':
      IconData(0xe12c, fontFamily: 'MaterialIcons', matchTextDirection: true),
  'call_split':
      IconData(0xe12d, fontFamily: 'MaterialIcons', matchTextDirection: true),
  'call_to_action': IconData(0xe12e, fontFamily: 'MaterialIcons'),
  'camera': IconData(0xe12f, fontFamily: 'MaterialIcons'),
  'camera_alt': IconData(0xe130, fontFamily: 'MaterialIcons'),
  'camera_enhance': IconData(0xe131, fontFamily: 'MaterialIcons'),
  'camera_front': IconData(0xe132, fontFamily: 'MaterialIcons'),
  'camera_indoor': IconData(0xe133, fontFamily: 'MaterialIcons'),
  'camera_outdoor': IconData(0xe134, fontFamily: 'MaterialIcons'),
  'camera_rear': IconData(0xe135, fontFamily: 'MaterialIcons'),
  'camera_roll': IconData(0xe136, fontFamily: 'MaterialIcons'),
  'cameraswitch': IconData(0xe137, fontFamily: 'MaterialIcons'),
  'campaign': IconData(0xe138, fontFamily: 'MaterialIcons'),
  'cancel': IconData(0xe139, fontFamily: 'MaterialIcons'),
  'cancel_presentation': IconData(0xe13a, fontFamily: 'MaterialIcons'),
  'cancel_schedule_send': IconData(0xe13b, fontFamily: 'MaterialIcons'),
  'candlestick_chart': IconData(0xf04ca, fontFamily: 'MaterialIcons'),
  'car_crash': IconData(0xf0793, fontFamily: 'MaterialIcons'),
  'car_rental': IconData(0xe13c, fontFamily: 'MaterialIcons'),
  'car_repair': IconData(0xe13d, fontFamily: 'MaterialIcons'),
  'card_giftcard': IconData(0xe13e, fontFamily: 'MaterialIcons'),
  'card_membership': IconData(0xe13f, fontFamily: 'MaterialIcons'),
  'card_travel': IconData(0xe140, fontFamily: 'MaterialIcons'),
  'carpenter': IconData(0xe141, fontFamily: 'MaterialIcons'),
  'cases': IconData(0xe142, fontFamily: 'MaterialIcons'),
  'casino': IconData(0xe143, fontFamily: 'MaterialIcons'),
  'cast': IconData(0xe144, fontFamily: 'MaterialIcons'),
  'cast_connected': IconData(0xe145, fontFamily: 'MaterialIcons'),
  'cast_for_education': IconData(0xe146, fontFamily: 'MaterialIcons'),
  'castle': IconData(0xf04cb, fontFamily: 'MaterialIcons'),
  'catching_pokemon': IconData(0xe147, fontFamily: 'MaterialIcons'),
  'category': IconData(0xe148, fontFamily: 'MaterialIcons'),
  'celebration': IconData(0xe149, fontFamily: 'MaterialIcons'),
  'cell_tower': IconData(0xf04cc, fontFamily: 'MaterialIcons'),
  'cell_wifi': IconData(0xe14a, fontFamily: 'MaterialIcons'),
  'center_focus_strong': IconData(0xe14b, fontFamily: 'MaterialIcons'),
  'center_focus_weak': IconData(0xe14c, fontFamily: 'MaterialIcons'),
  'chair': IconData(0xe14d, fontFamily: 'MaterialIcons'),
  'chair_alt': IconData(0xe14e, fontFamily: 'MaterialIcons'),
  'chalet': IconData(0xe14f, fontFamily: 'MaterialIcons'),
  'change_circle': IconData(0xe150, fontFamily: 'MaterialIcons'),
  'change_history': IconData(0xe151, fontFamily: 'MaterialIcons'),
  'charging_station': IconData(0xe152, fontFamily: 'MaterialIcons'),
  'chat': IconData(0xe153, fontFamily: 'MaterialIcons'),
  'chat_bubble': IconData(0xe154, fontFamily: 'MaterialIcons'),
  'chat_bubble_outline': IconData(0xe155, fontFamily: 'MaterialIcons'),
  'check': IconData(0xe156, fontFamily: 'MaterialIcons'),
  'check_box': IconData(0xe157, fontFamily: 'MaterialIcons'),
  'check_box_outline_blank': IconData(0xe158, fontFamily: 'MaterialIcons'),
  'check_circle': IconData(0xe159, fontFamily: 'MaterialIcons'),
  'check_circle_outline': IconData(0xe15a, fontFamily: 'MaterialIcons'),
  'checklist': IconData(0xe15b, fontFamily: 'MaterialIcons'),
  'checklist_rtl': IconData(0xe15c, fontFamily: 'MaterialIcons'),
  'checkroom': IconData(0xe15d, fontFamily: 'MaterialIcons'),
  'chevron_left':
      IconData(0xe15e, fontFamily: 'MaterialIcons', matchTextDirection: true),
  'chevron_right':
      IconData(0xe15f, fontFamily: 'MaterialIcons', matchTextDirection: true),
  'child_care': IconData(0xe160, fontFamily: 'MaterialIcons'),
  'child_friendly': IconData(0xe161, fontFamily: 'MaterialIcons'),
  'chrome_reader_mode':
      IconData(0xe162, fontFamily: 'MaterialIcons', matchTextDirection: true),
  'church': IconData(0xf04cd, fontFamily: 'MaterialIcons'),
  'circle': IconData(0xe163, fontFamily: 'MaterialIcons'),
  'circle_notifications': IconData(0xe164, fontFamily: 'MaterialIcons'),
  'class': IconData(0xe165, fontFamily: 'MaterialIcons'),
  'clean_hands': IconData(0xe166, fontFamily: 'MaterialIcons'),
  'cleaning_services': IconData(0xe167, fontFamily: 'MaterialIcons'),
  'clear': IconData(0xe168, fontFamily: 'MaterialIcons'),
  'clear_all': IconData(0xe169, fontFamily: 'MaterialIcons'),
  'close': IconData(0xe16a, fontFamily: 'MaterialIcons'),
  'close_fullscreen': IconData(0xe16b, fontFamily: 'MaterialIcons'),
  'closed_caption': IconData(0xe16c, fontFamily: 'MaterialIcons'),
  'closed_caption_disabled': IconData(0xe16d, fontFamily: 'MaterialIcons'),
  'closed_caption_off': IconData(0xe16e, fontFamily: 'MaterialIcons'),
  'cloud': IconData(0xe16f, fontFamily: 'MaterialIcons'),
  'cloud_circle': IconData(0xe170, fontFamily: 'MaterialIcons'),
  'cloud_done': IconData(0xe171, fontFamily: 'MaterialIcons'),
  'cloud_download': IconData(0xe172, fontFamily: 'MaterialIcons'),
  'cloud_off': IconData(0xe173, fontFamily: 'MaterialIcons'),
  'cloud_queue': IconData(0xe174, fontFamily: 'MaterialIcons'),
  'cloud_sync': IconData(0xf04ce, fontFamily: 'MaterialIcons'),
  'cloud_upload': IconData(0xe175, fontFamily: 'MaterialIcons'),
  'cloudy_snowing': IconData(0xf04cf, fontFamily: 'MaterialIcons'),
  'co2': IconData(0xf04d0, fontFamily: 'MaterialIcons'),
  'co_present': IconData(0xf04d1, fontFamily: 'MaterialIcons'),
  'code': IconData(0xe176, fontFamily: 'MaterialIcons'),
  'code_off': IconData(0xe177, fontFamily: 'MaterialIcons'),
  'coffee': IconData(0xe178, fontFamily: 'MaterialIcons'),
  'coffee_maker': IconData(0xe179, fontFamily: 'MaterialIcons'),
  'collections': IconData(0xe17a, fontFamily: 'MaterialIcons'),
  'collections_bookmark': IconData(0xe17b, fontFamily: 'MaterialIcons'),
  'color_lens': IconData(0xe17c, fontFamily: 'MaterialIcons'),
  'colorize': IconData(0xe17d, fontFamily: 'MaterialIcons'),
  'comment': IconData(0xe17e, fontFamily: 'MaterialIcons'),
  'comment_bank': IconData(0xe17f, fontFamily: 'MaterialIcons'),
  'comments_disabled': IconData(0xf04d2, fontFamily: 'MaterialIcons'),
  'commit': IconData(0xf04d3, fontFamily: 'MaterialIcons'),
  'commute': IconData(0xe180, fontFamily: 'MaterialIcons'),
  'compare': IconData(0xe181, fontFamily: 'MaterialIcons'),
  'compare_arrows': IconData(0xe182, fontFamily: 'MaterialIcons'),
  'compass_calibration': IconData(0xe183, fontFamily: 'MaterialIcons'),
  'compost': IconData(0xf04d4, fontFamily: 'MaterialIcons'),
  'compress': IconData(0xe184, fontFamily: 'MaterialIcons'),
  'computer': IconData(0xe185, fontFamily: 'MaterialIcons'),
  'confirmation_num': IconData(0xe186, fontFamily: 'MaterialIcons'),
  'confirmation_number': IconData(0xe186, fontFamily: 'MaterialIcons'),
  'connect_without_contact': IconData(0xe187, fontFamily: 'MaterialIcons'),
  'connected_tv': IconData(0xe188, fontFamily: 'MaterialIcons'),
  'connecting_airports': IconData(0xf04d5, fontFamily: 'MaterialIcons'),
  'construction': IconData(0xe189, fontFamily: 'MaterialIcons'),
  'contact_emergency': IconData(0xf0857, fontFamily: 'MaterialIcons'),
  'contact_mail': IconData(0xe18a, fontFamily: 'MaterialIcons'),
  'contact_page': IconData(0xe18b, fontFamily: 'MaterialIcons'),
  'contact_phone': IconData(0xe18c, fontFamily: 'MaterialIcons'),
  'contact_support': IconData(0xe18d, fontFamily: 'MaterialIcons'),
  'contactless': IconData(0xe18e, fontFamily: 'MaterialIcons'),
  'contacts': IconData(0xe18f, fontFamily: 'MaterialIcons'),
  'content_copy': IconData(0xe190, fontFamily: 'MaterialIcons'),
  'content_cut': IconData(0xe191, fontFamily: 'MaterialIcons'),
  'content_paste': IconData(0xe192, fontFamily: 'MaterialIcons'),
  'content_paste_go': IconData(0xf04d6, fontFamily: 'MaterialIcons'),
  'content_paste_off': IconData(0xe193, fontFamily: 'MaterialIcons'),
  'content_paste_search': IconData(0xf04d7, fontFamily: 'MaterialIcons'),
  'contrast': IconData(0xf04d8, fontFamily: 'MaterialIcons'),
  'control_camera': IconData(0xe194, fontFamily: 'MaterialIcons'),
  'control_point': IconData(0xe195, fontFamily: 'MaterialIcons'),
  'control_point_duplicate': IconData(0xe196, fontFamily: 'MaterialIcons'),
  'conveyor_belt': IconData(0xf0858, fontFamily: 'MaterialIcons'),
  'cookie': IconData(0xf04d9, fontFamily: 'MaterialIcons'),
  'copy': IconData(0xe190, fontFamily: 'MaterialIcons'),
  'copy_all': IconData(0xe197, fontFamily: 'MaterialIcons'),
  'copyright': IconData(0xe198, fontFamily: 'MaterialIcons'),
  'coronavirus': IconData(0xe199, fontFamily: 'MaterialIcons'),
  'corporate_fare': IconData(0xe19a, fontFamily: 'MaterialIcons'),
  'cottage': IconData(0xe19b, fontFamily: 'MaterialIcons'),
  'countertops': IconData(0xe19c, fontFamily: 'MaterialIcons'),
  'create': IconData(0xe19d, fontFamily: 'MaterialIcons'),
  'create_new_folder': IconData(0xe19e, fontFamily: 'MaterialIcons'),
  'credit_card': IconData(0xe19f, fontFamily: 'MaterialIcons'),
  'credit_card_off': IconData(0xe1a0, fontFamily: 'MaterialIcons'),
  'credit_score': IconData(0xe1a1, fontFamily: 'MaterialIcons'),
  'crib': IconData(0xe1a2, fontFamily: 'MaterialIcons'),
  'crisis_alert': IconData(0xf0794, fontFamily: 'MaterialIcons'),
  'crop': IconData(0xe1a3, fontFamily: 'MaterialIcons'),
  'crop_16_9': IconData(0xe1a4, fontFamily: 'MaterialIcons'),
  'crop_3_2': IconData(0xe1a5, fontFamily: 'MaterialIcons'),
  'crop_5_4': IconData(0xe1a6, fontFamily: 'MaterialIcons'),
  'crop_7_5': IconData(0xe1a7, fontFamily: 'MaterialIcons'),
  'crop_din': IconData(0xe1a8, fontFamily: 'MaterialIcons'),
  'crop_free': IconData(0xe1a9, fontFamily: 'MaterialIcons'),
  'crop_landscape': IconData(0xe1aa, fontFamily: 'MaterialIcons'),
  'crop_original': IconData(0xe1ab, fontFamily: 'MaterialIcons'),
  'crop_portrait': IconData(0xe1ac, fontFamily: 'MaterialIcons'),
  'crop_rotate': IconData(0xe1ad, fontFamily: 'MaterialIcons'),
  'crop_square': IconData(0xe1ae, fontFamily: 'MaterialIcons'),
  'cruelty_free': IconData(0xf04da, fontFamily: 'MaterialIcons'),
  'css': IconData(0xf04db, fontFamily: 'MaterialIcons'),
  'currency_bitcoin': IconData(0xf06bc, fontFamily: 'MaterialIcons'),
  'currency_exchange': IconData(0xf04dc, fontFamily: 'MaterialIcons'),
  'currency_franc': IconData(0xf04dd, fontFamily: 'MaterialIcons'),
  'currency_lira': IconData(0xf04de, fontFamily: 'MaterialIcons'),
  'currency_pound': IconData(0xf04df, fontFamily: 'MaterialIcons'),
  'currency_ruble': IconData(0xf04e0, fontFamily: 'MaterialIcons'),
  'currency_rupee': IconData(0xf04e1, fontFamily: 'MaterialIcons'),
  'currency_yen': IconData(0xf04e2, fontFamily: 'MaterialIcons'),
  'currency_yuan': IconData(0xf04e3, fontFamily: 'MaterialIcons'),
  'curtains': IconData(0xf0795, fontFamily: 'MaterialIcons'),
  'curtains_closed': IconData(0xf0796, fontFamily: 'MaterialIcons'),
  'cut': IconData(0xe191, fontFamily: 'MaterialIcons'),
  'cyclone': IconData(0xf0797, fontFamily: 'MaterialIcons'),
  'dangerous': IconData(0xe1af, fontFamily: 'MaterialIcons'),
  'dark_mode': IconData(0xe1b0, fontFamily: 'MaterialIcons'),
  'dashboard': IconData(0xe1b1, fontFamily: 'MaterialIcons'),
  'dashboard_customize': IconData(0xe1b2, fontFamily: 'MaterialIcons'),
  'data_array': IconData(0xf04e4, fontFamily: 'MaterialIcons'),
  'data_exploration': IconData(0xf04e5, fontFamily: 'MaterialIcons'),
  'data_object': IconData(0xf04e6, fontFamily: 'MaterialIcons'),
  'data_saver_off': IconData(0xe1b3, fontFamily: 'MaterialIcons'),
  'data_saver_on': IconData(0xe1b4, fontFamily: 'MaterialIcons'),
  'data_thresholding': IconData(0xf04e7, fontFamily: 'MaterialIcons'),
  'data_usage': IconData(0xe1b5, fontFamily: 'MaterialIcons'),
  'dataset': IconData(0xf0798, fontFamily: 'MaterialIcons'),
  'dataset_linked': IconData(0xf0799, fontFamily: 'MaterialIcons'),
  'date_range': IconData(0xe1b6, fontFamily: 'MaterialIcons'),
  'deblur': IconData(0xf04e8, fontFamily: 'MaterialIcons'),
  'deck': IconData(0xe1b7, fontFamily: 'MaterialIcons'),
  'dehaze': IconData(0xe1b8, fontFamily: 'MaterialIcons'),
  'delete': IconData(0xe1b9, fontFamily: 'MaterialIcons'),
  'delete_forever': IconData(0xe1ba, fontFamily: 'MaterialIcons'),
  'delete_outline': IconData(0xe1bb, fontFamily: 'MaterialIcons'),
  'delete_sweep': IconData(0xe1bc, fontFamily: 'MaterialIcons'),
  'delivery_dining': IconData(0xe1bd, fontFamily: 'MaterialIcons'),
  'density_large': IconData(0xf04e9, fontFamily: 'MaterialIcons'),
  'density_medium': IconData(0xf04ea, fontFamily: 'MaterialIcons'),
  'density_small': IconData(0xf04eb, fontFamily: 'MaterialIcons'),
  'departure_board': IconData(0xe1be, fontFamily: 'MaterialIcons'),
  'description': IconData(0xe1bf, fontFamily: 'MaterialIcons'),
  'deselect': IconData(0xf04ec, fontFamily: 'MaterialIcons'),
  'design_services': IconData(0xe1c0, fontFamily: 'MaterialIcons'),
  'desk': IconData(0xf079a, fontFamily: 'MaterialIcons'),
  'desktop_access_disabled': IconData(0xe1c1, fontFamily: 'MaterialIcons'),
  'desktop_mac': IconData(0xe1c2, fontFamily: 'MaterialIcons'),
  'desktop_windows': IconData(0xe1c3, fontFamily: 'MaterialIcons'),
  'details': IconData(0xe1c4, fontFamily: 'MaterialIcons'),
  'developer_board': IconData(0xe1c5, fontFamily: 'MaterialIcons'),
  'developer_board_off': IconData(0xe1c6, fontFamily: 'MaterialIcons'),
  'developer_mode': IconData(0xe1c7, fontFamily: 'MaterialIcons'),
  'device_hub': IconData(0xe1c8, fontFamily: 'MaterialIcons'),
  'device_thermostat': IconData(0xe1c9, fontFamily: 'MaterialIcons'),
  'device_unknown':
      IconData(0xe1ca, fontFamily: 'MaterialIcons', matchTextDirection: true),
  'devices': IconData(0xe1cb, fontFamily: 'MaterialIcons'),
  'devices_fold': IconData(0xf079b, fontFamily: 'MaterialIcons'),
  'devices_other': IconData(0xe1cc, fontFamily: 'MaterialIcons'),
  'dew_point': IconData(0xf0859, fontFamily: 'MaterialIcons'),
  'dialer_sip': IconData(0xe1cd, fontFamily: 'MaterialIcons'),
  'dialpad': IconData(0xe1ce, fontFamily: 'MaterialIcons'),
  'diamond': IconData(0xf04ed, fontFamily: 'MaterialIcons'),
  'difference': IconData(0xf04ee, fontFamily: 'MaterialIcons'),
  'dining': IconData(0xe1cf, fontFamily: 'MaterialIcons'),
  'dinner_dining': IconData(0xe1d0, fontFamily: 'MaterialIcons'),
  'directions': IconData(0xe1d1, fontFamily: 'MaterialIcons'),
  'directions_bike': IconData(0xe1d2, fontFamily: 'MaterialIcons'),
  'directions_boat': IconData(0xe1d3, fontFamily: 'MaterialIcons'),
  'directions_boat_filled': IconData(0xe1d4, fontFamily: 'MaterialIcons'),
  'directions_bus': IconData(0xe1d5, fontFamily: 'MaterialIcons'),
  'directions_bus_filled': IconData(0xe1d6, fontFamily: 'MaterialIcons'),
  'directions_car': IconData(0xe1d7, fontFamily: 'MaterialIcons'),
  'directions_car_filled': IconData(0xe1d8, fontFamily: 'MaterialIcons'),
  'directions_ferry': IconData(0xe1d3, fontFamily: 'MaterialIcons'),
  'directions_off': IconData(0xe1d9, fontFamily: 'MaterialIcons'),
  'directions_railway': IconData(0xe1da, fontFamily: 'MaterialIcons'),
  'directions_railway_filled': IconData(0xe1db, fontFamily: 'MaterialIcons'),
  'directions_run': IconData(0xe1dc, fontFamily: 'MaterialIcons'),
  'directions_subway': IconData(0xe1dd, fontFamily: 'MaterialIcons'),
  'directions_subway_filled': IconData(0xe1de, fontFamily: 'MaterialIcons'),
  'directions_train': IconData(0xe1da, fontFamily: 'MaterialIcons'),
  'directions_transit': IconData(0xe1df, fontFamily: 'MaterialIcons'),
  'directions_transit_filled': IconData(0xe1e0, fontFamily: 'MaterialIcons'),
  'directions_walk': IconData(0xe1e1, fontFamily: 'MaterialIcons'),
  'dirty_lens': IconData(0xe1e2, fontFamily: 'MaterialIcons'),
  'disabled_by_default': IconData(0xe1e3, fontFamily: 'MaterialIcons'),
  'disabled_visible': IconData(0xf04ef, fontFamily: 'MaterialIcons'),
  'disc_full': IconData(0xe1e4, fontFamily: 'MaterialIcons'),
  'discord': IconData(0xf04f0, fontFamily: 'MaterialIcons'),
  'discount': IconData(0xf06bd, fontFamily: 'MaterialIcons'),
  'display_settings': IconData(0xf04f1, fontFamily: 'MaterialIcons'),
  'diversity_1': IconData(0xf085a, fontFamily: 'MaterialIcons'),
  'diversity_2': IconData(0xf085b, fontFamily: 'MaterialIcons'),
  'diversity_3': IconData(0xf085c, fontFamily: 'MaterialIcons'),
  'dnd_forwardslash': IconData(0xe1eb, fontFamily: 'MaterialIcons'),
  'dns': IconData(0xe1e5, fontFamily: 'MaterialIcons'),
  'do_disturb': IconData(0xe1e6, fontFamily: 'MaterialIcons'),
  'do_disturb_alt': IconData(0xe1e7, fontFamily: 'MaterialIcons'),
  'do_disturb_off': IconData(0xe1e8, fontFamily: 'MaterialIcons'),
  'do_disturb_on': IconData(0xe1e9, fontFamily: 'MaterialIcons'),
  'do_not_disturb': IconData(0xe1ea, fontFamily: 'MaterialIcons'),
  'do_not_disturb_alt': IconData(0xe1eb, fontFamily: 'MaterialIcons'),
  'do_not_disturb_off': IconData(0xe1ec, fontFamily: 'MaterialIcons'),
  'do_not_disturb_on': IconData(0xe1ed, fontFamily: 'MaterialIcons'),
  'do_not_disturb_on_total_silence':
      IconData(0xe1ee, fontFamily: 'MaterialIcons'),
  'do_not_step': IconData(0xe1ef, fontFamily: 'MaterialIcons'),
  'do_not_touch': IconData(0xe1f0, fontFamily: 'MaterialIcons'),
  'dock': IconData(0xe1f1, fontFamily: 'MaterialIcons'),
  'document_scanner': IconData(0xe1f2, fontFamily: 'MaterialIcons'),
  'domain': IconData(0xe1f3, fontFamily: 'MaterialIcons'),
  'domain_add': IconData(0xf04f2, fontFamily: 'MaterialIcons'),
  'domain_disabled': IconData(0xe1f4, fontFamily: 'MaterialIcons'),
  'domain_verification': IconData(0xe1f5, fontFamily: 'MaterialIcons'),
  'done': IconData(0xe1f6, fontFamily: 'MaterialIcons'),
  'done_all': IconData(0xe1f7, fontFamily: 'MaterialIcons'),
  'done_outline': IconData(0xe1f8, fontFamily: 'MaterialIcons'),
  'donut_large': IconData(0xe1f9, fontFamily: 'MaterialIcons'),
  'donut_small': IconData(0xe1fa, fontFamily: 'MaterialIcons'),
  'door_back': IconData(0xe1fb, fontFamily: 'MaterialIcons'),
  'door_front': IconData(0xe1fc, fontFamily: 'MaterialIcons'),
  'door_sliding': IconData(0xe1fd, fontFamily: 'MaterialIcons'),
  'doorbell': IconData(0xe1fe, fontFamily: 'MaterialIcons'),
  'double_arrow': IconData(0xe1ff, fontFamily: 'MaterialIcons'),
  'downhill_skiing': IconData(0xe200, fontFamily: 'MaterialIcons'),
  'download': IconData(0xe201, fontFamily: 'MaterialIcons'),
  'download_done': IconData(0xe202, fontFamily: 'MaterialIcons'),
  'download_for_offline': IconData(0xe203, fontFamily: 'MaterialIcons'),
  'downloading': IconData(0xe204, fontFamily: 'MaterialIcons'),
  'drafts': IconData(0xe205, fontFamily: 'MaterialIcons'),
  'drag_handle': IconData(0xe206, fontFamily: 'MaterialIcons'),
  'drag_indicator': IconData(0xe207, fontFamily: 'MaterialIcons'),
  'draw': IconData(0xf04f3, fontFamily: 'MaterialIcons'),
  'drive_eta': IconData(0xe208, fontFamily: 'MaterialIcons'),
  'drive_file_move': IconData(0xe209, fontFamily: 'MaterialIcons'),
  'drive_file_move_outline': IconData(0xe20a, fontFamily: 'MaterialIcons'),
  'drive_file_move_rtl': IconData(0xf04f4, fontFamily: 'MaterialIcons'),
  'drive_file_rename_outline': IconData(0xe20b, fontFamily: 'MaterialIcons'),
  'drive_folder_upload': IconData(0xe20c, fontFamily: 'MaterialIcons'),
  'dry': IconData(0xe20d, fontFamily: 'MaterialIcons'),
  'dry_cleaning': IconData(0xe20e, fontFamily: 'MaterialIcons'),
  'duo': IconData(0xe20f, fontFamily: 'MaterialIcons'),
  'dvr':
      IconData(0xe210, fontFamily: 'MaterialIcons', matchTextDirection: true),
  'dynamic_feed': IconData(0xe211, fontFamily: 'MaterialIcons'),
  'dynamic_form': IconData(0xe212, fontFamily: 'MaterialIcons'),
  'e_mobiledata': IconData(0xe213, fontFamily: 'MaterialIcons'),
  'earbuds': IconData(0xe214, fontFamily: 'MaterialIcons'),
  'earbuds_battery': IconData(0xe215, fontFamily: 'MaterialIcons'),
  'east': IconData(0xe216, fontFamily: 'MaterialIcons'),
  'eco': IconData(0xe217, fontFamily: 'MaterialIcons'),
  'edgesensor_high': IconData(0xe218, fontFamily: 'MaterialIcons'),
  'edgesensor_low': IconData(0xe219, fontFamily: 'MaterialIcons'),
  'edit': IconData(0xe21a, fontFamily: 'MaterialIcons'),
  'edit_attributes': IconData(0xe21b, fontFamily: 'MaterialIcons'),
  'edit_calendar': IconData(0xf04f5, fontFamily: 'MaterialIcons'),
  'edit_document': IconData(0xf085d, fontFamily: 'MaterialIcons'),
  'edit_location': IconData(0xe21c, fontFamily: 'MaterialIcons'),
  'edit_location_alt': IconData(0xe21d, fontFamily: 'MaterialIcons'),
  'edit_note': IconData(0xf04f6, fontFamily: 'MaterialIcons'),
  'edit_notifications': IconData(0xe21e, fontFamily: 'MaterialIcons'),
  'edit_off': IconData(0xe21f, fontFamily: 'MaterialIcons'),
  'edit_road': IconData(0xe220, fontFamily: 'MaterialIcons'),
  'edit_square': IconData(0xf085e, fontFamily: 'MaterialIcons'),
  'egg': IconData(0xf04f8, fontFamily: 'MaterialIcons'),
  'egg_alt': IconData(0xf04f7, fontFamily: 'MaterialIcons'),
  'eject': IconData(0xe221, fontFamily: 'MaterialIcons'),
  'elderly': IconData(0xe222, fontFamily: 'MaterialIcons'),
  'elderly_woman': IconData(0xf04f9, fontFamily: 'MaterialIcons'),
  'electric_bike': IconData(0xe223, fontFamily: 'MaterialIcons'),
  'electric_bolt': IconData(0xf079c, fontFamily: 'MaterialIcons'),
  'electric_car': IconData(0xe224, fontFamily: 'MaterialIcons'),
  'electric_meter': IconData(0xf079d, fontFamily: 'MaterialIcons'),
  'electric_moped': IconData(0xe225, fontFamily: 'MaterialIcons'),
  'electric_rickshaw': IconData(0xe226, fontFamily: 'MaterialIcons'),
  'electric_scooter': IconData(0xe227, fontFamily: 'MaterialIcons'),
  'electrical_services': IconData(0xe228, fontFamily: 'MaterialIcons'),
  'elevator': IconData(0xe229, fontFamily: 'MaterialIcons'),
  'email': IconData(0xe22a, fontFamily: 'MaterialIcons'),
  'emergency': IconData(0xf04fa, fontFamily: 'MaterialIcons'),
  'emergency_recording': IconData(0xf079e, fontFamily: 'MaterialIcons'),
  'emergency_share': IconData(0xf079f, fontFamily: 'MaterialIcons'),
  'emoji_emotions': IconData(0xe22b, fontFamily: 'MaterialIcons'),
  'emoji_events': IconData(0xe22c, fontFamily: 'MaterialIcons'),
  'emoji_flags': IconData(0xe22d, fontFamily: 'MaterialIcons'),
  'emoji_food_beverage': IconData(0xe22e, fontFamily: 'MaterialIcons'),
  'emoji_nature': IconData(0xe22f, fontFamily: 'MaterialIcons'),
  'emoji_objects': IconData(0xe230, fontFamily: 'MaterialIcons'),
  'emoji_people': IconData(0xe231, fontFamily: 'MaterialIcons'),
  'emoji_symbols': IconData(0xe232, fontFamily: 'MaterialIcons'),
  'emoji_transportation': IconData(0xe233, fontFamily: 'MaterialIcons'),
  'energy_savings_leaf': IconData(0xf07a0, fontFamily: 'MaterialIcons'),
  'engineering': IconData(0xe234, fontFamily: 'MaterialIcons'),
  'enhance_photo_translate': IconData(0xe131, fontFamily: 'MaterialIcons'),
  'enhanced_encryption': IconData(0xe235, fontFamily: 'MaterialIcons'),
  'equalizer': IconData(0xe236, fontFamily: 'MaterialIcons'),
  'error': IconData(0xe237, fontFamily: 'MaterialIcons'),
  'error_outline': IconData(0xe238, fontFamily: 'MaterialIcons'),
  'escalator': IconData(0xe239, fontFamily: 'MaterialIcons'),
  'escalator_warning': IconData(0xe23a, fontFamily: 'MaterialIcons'),
  'euro': IconData(0xe23b, fontFamily: 'MaterialIcons'),
  'euro_symbol': IconData(0xe23c, fontFamily: 'MaterialIcons'),
  'ev_station': IconData(0xe23d, fontFamily: 'MaterialIcons'),
  'event': IconData(0xe23e, fontFamily: 'MaterialIcons'),
  'event_available': IconData(0xe23f, fontFamily: 'MaterialIcons'),
  'event_busy': IconData(0xe240, fontFamily: 'MaterialIcons'),
  'event_note':
      IconData(0xe241, fontFamily: 'MaterialIcons', matchTextDirection: true),
  'event_repeat': IconData(0xf04fb, fontFamily: 'MaterialIcons'),
  'event_seat': IconData(0xe242, fontFamily: 'MaterialIcons'),
  'exit_to_app': IconData(0xe243, fontFamily: 'MaterialIcons'),
  'expand': IconData(0xe244, fontFamily: 'MaterialIcons'),
  'expand_circle_down': IconData(0xf04fc, fontFamily: 'MaterialIcons'),
  'expand_less': IconData(0xe245, fontFamily: 'MaterialIcons'),
  'expand_more': IconData(0xe246, fontFamily: 'MaterialIcons'),
  'explicit': IconData(0xe247, fontFamily: 'MaterialIcons'),
  'explore': IconData(0xe248, fontFamily: 'MaterialIcons'),
  'explore_off': IconData(0xe249, fontFamily: 'MaterialIcons'),
  'exposure': IconData(0xe24a, fontFamily: 'MaterialIcons'),
  'exposure_minus_1': IconData(0xe24b, fontFamily: 'MaterialIcons'),
  'exposure_minus_2': IconData(0xe24c, fontFamily: 'MaterialIcons'),
  'exposure_neg_1': IconData(0xe24b, fontFamily: 'MaterialIcons'),
  'exposure_neg_2': IconData(0xe24c, fontFamily: 'MaterialIcons'),
  'exposure_plus_1': IconData(0xe24d, fontFamily: 'MaterialIcons'),
  'exposure_plus_2': IconData(0xe24e, fontFamily: 'MaterialIcons'),
  'exposure_zero': IconData(0xe24f, fontFamily: 'MaterialIcons'),
  'extension': IconData(0xe250, fontFamily: 'MaterialIcons'),
  'extension_off': IconData(0xe251, fontFamily: 'MaterialIcons'),
  'face': IconData(0xe252, fontFamily: 'MaterialIcons'),
  'face_2': IconData(0xf085f, fontFamily: 'MaterialIcons'),
  'face_3': IconData(0xf0860, fontFamily: 'MaterialIcons'),
  'face_4': IconData(0xf0861, fontFamily: 'MaterialIcons'),
  'face_5': IconData(0xf0862, fontFamily: 'MaterialIcons'),
  'face_6': IconData(0xf0863, fontFamily: 'MaterialIcons'),
  'face_retouching_natural': IconData(0xe253, fontFamily: 'MaterialIcons'),
  'face_retouching_off': IconData(0xe254, fontFamily: 'MaterialIcons'),
  'facebook': IconData(0xe255, fontFamily: 'MaterialIcons'),
  'fact_check': IconData(0xe256, fontFamily: 'MaterialIcons'),
  'factory': IconData(0xf04fd, fontFamily: 'MaterialIcons'),
  'family_restroom': IconData(0xe257, fontFamily: 'MaterialIcons'),
  'fast_forward': IconData(0xe258, fontFamily: 'MaterialIcons'),
  'fast_rewind': IconData(0xe259, fontFamily: 'MaterialIcons'),
  'fastfood': IconData(0xe25a, fontFamily: 'MaterialIcons'),
  'favorite': IconData(0xe25b, fontFamily: 'MaterialIcons'),
  'favorite_border': IconData(0xe25c, fontFamily: 'MaterialIcons'),
  'favorite_outline': IconData(0xe25c, fontFamily: 'MaterialIcons'),
  'fax': IconData(0xf04fe, fontFamily: 'MaterialIcons'),
  'featured_play_list':
      IconData(0xe25d, fontFamily: 'MaterialIcons', matchTextDirection: true),
  'featured_video':
      IconData(0xe25e, fontFamily: 'MaterialIcons', matchTextDirection: true),
  'feed': IconData(0xe25f, fontFamily: 'MaterialIcons'),
  'feedback': IconData(0xe260, fontFamily: 'MaterialIcons'),
  'female': IconData(0xe261, fontFamily: 'MaterialIcons'),
  'fence': IconData(0xe262, fontFamily: 'MaterialIcons'),
  'festival': IconData(0xe263, fontFamily: 'MaterialIcons'),
  'fiber_dvr': IconData(0xe264, fontFamily: 'MaterialIcons'),
  'fiber_manual_record': IconData(0xe265, fontFamily: 'MaterialIcons'),
  'fiber_new': IconData(0xe266, fontFamily: 'MaterialIcons'),
  'fiber_pin': IconData(0xe267, fontFamily: 'MaterialIcons'),
  'fiber_smart_record': IconData(0xe268, fontFamily: 'MaterialIcons'),
  'file_copy': IconData(0xe269, fontFamily: 'MaterialIcons'),
  'file_download': IconData(0xe26a, fontFamily: 'MaterialIcons'),
  'file_download_done': IconData(0xe26b, fontFamily: 'MaterialIcons'),
  'file_download_off': IconData(0xe26c, fontFamily: 'MaterialIcons'),
  'file_open': IconData(0xf04ff, fontFamily: 'MaterialIcons'),
  'file_present': IconData(0xe26d, fontFamily: 'MaterialIcons'),
  'file_upload': IconData(0xe26e, fontFamily: 'MaterialIcons'),
  'file_upload_off': IconData(0xf0864, fontFamily: 'MaterialIcons'),
  'filter': IconData(0xe26f, fontFamily: 'MaterialIcons'),
  'filter_1': IconData(0xe270, fontFamily: 'MaterialIcons'),
  'filter_2': IconData(0xe271, fontFamily: 'MaterialIcons'),
  'filter_3': IconData(0xe272, fontFamily: 'MaterialIcons'),
  'filter_4': IconData(0xe273, fontFamily: 'MaterialIcons'),
  'filter_5': IconData(0xe274, fontFamily: 'MaterialIcons'),
  'filter_6': IconData(0xe275, fontFamily: 'MaterialIcons'),
  'filter_7': IconData(0xe276, fontFamily: 'MaterialIcons'),
  'filter_8': IconData(0xe277, fontFamily: 'MaterialIcons'),
  'filter_9': IconData(0xe278, fontFamily: 'MaterialIcons'),
  'filter_9_plus': IconData(0xe279, fontFamily: 'MaterialIcons'),
  'filter_alt': IconData(0xe27a, fontFamily: 'MaterialIcons'),
  'filter_alt_off': IconData(0xf0500, fontFamily: 'MaterialIcons'),
  'filter_b_and_w': IconData(0xe27b, fontFamily: 'MaterialIcons'),
  'filter_center_focus': IconData(0xe27c, fontFamily: 'MaterialIcons'),
  'filter_drama': IconData(0xe27d, fontFamily: 'MaterialIcons'),
  'filter_frames': IconData(0xe27e, fontFamily: 'MaterialIcons'),
  'filter_hdr': IconData(0xe27f, fontFamily: 'MaterialIcons'),
  'filter_list': IconData(0xe280, fontFamily: 'MaterialIcons'),
  'filter_list_alt': IconData(0xe281, fontFamily: 'MaterialIcons'),
  'filter_list_off': IconData(0xf0501, fontFamily: 'MaterialIcons'),
  'filter_none': IconData(0xe282, fontFamily: 'MaterialIcons'),
  'filter_tilt_shift': IconData(0xe283, fontFamily: 'MaterialIcons'),
  'filter_vintage': IconData(0xe284, fontFamily: 'MaterialIcons'),
  'find_in_page': IconData(0xe285, fontFamily: 'MaterialIcons'),
  'find_replace': IconData(0xe286, fontFamily: 'MaterialIcons'),
  'fingerprint': IconData(0xe287, fontFamily: 'MaterialIcons'),
  'fire_extinguisher': IconData(0xe288, fontFamily: 'MaterialIcons'),
  'fire_hydrant': IconData(0xe289, fontFamily: 'MaterialIcons'),
  'fire_hydrant_alt': IconData(0xf07a1, fontFamily: 'MaterialIcons'),
  'fire_truck': IconData(0xf07a2, fontFamily: 'MaterialIcons'),
  'fireplace': IconData(0xe28a, fontFamily: 'MaterialIcons'),
  'first_page':
      IconData(0xe28b, fontFamily: 'MaterialIcons', matchTextDirection: true),
  'fit_screen': IconData(0xe28c, fontFamily: 'MaterialIcons'),
  'fitbit': IconData(0xf0502, fontFamily: 'MaterialIcons'),
  'fitness_center': IconData(0xe28d, fontFamily: 'MaterialIcons'),
  'flag': IconData(0xe28e, fontFamily: 'MaterialIcons'),
  'flag_circle': IconData(0xf0503, fontFamily: 'MaterialIcons'),
  'flaky': IconData(0xe28f, fontFamily: 'MaterialIcons'),
  'flare': IconData(0xe290, fontFamily: 'MaterialIcons'),
  'flash_auto': IconData(0xe291, fontFamily: 'MaterialIcons'),
  'flash_off': IconData(0xe292, fontFamily: 'MaterialIcons'),
  'flash_on': IconData(0xe293, fontFamily: 'MaterialIcons'),
  'flashlight_off': IconData(0xe294, fontFamily: 'MaterialIcons'),
  'flashlight_on': IconData(0xe295, fontFamily: 'MaterialIcons'),
  'flatware': IconData(0xe296, fontFamily: 'MaterialIcons'),
  'flight': IconData(0xe297, fontFamily: 'MaterialIcons'),
  'flight_class': IconData(0xf0504, fontFamily: 'MaterialIcons'),
  'flight_land':
      IconData(0xe298, fontFamily: 'MaterialIcons', matchTextDirection: true),
  'flight_takeoff':
      IconData(0xe299, fontFamily: 'MaterialIcons', matchTextDirection: true),
  'flip': IconData(0xe29a, fontFamily: 'MaterialIcons'),
  'flip_camera_android': IconData(0xe29b, fontFamily: 'MaterialIcons'),
  'flip_camera_ios': IconData(0xe29c, fontFamily: 'MaterialIcons'),
  'flip_to_back': IconData(0xe29d, fontFamily: 'MaterialIcons'),
  'flip_to_front': IconData(0xe29e, fontFamily: 'MaterialIcons'),
  'flood': IconData(0xf07a3, fontFamily: 'MaterialIcons'),
  'flourescent': IconData(0xf0865, fontFamily: 'MaterialIcons'),
  'fluorescent': IconData(0xf0865, fontFamily: 'MaterialIcons'),
  'flutter_dash': IconData(0xe2a0, fontFamily: 'MaterialIcons'),
  'fmd_bad': IconData(0xe2a1, fontFamily: 'MaterialIcons'),
  'fmd_good': IconData(0xe2a2, fontFamily: 'MaterialIcons'),
  'foggy': IconData(0xf0505, fontFamily: 'MaterialIcons'),
  'folder': IconData(0xe2a3, fontFamily: 'MaterialIcons'),
  'folder_copy': IconData(0xf0506, fontFamily: 'MaterialIcons'),
  'folder_delete': IconData(0xf0507, fontFamily: 'MaterialIcons'),
  'folder_off': IconData(0xf0508, fontFamily: 'MaterialIcons'),
  'folder_open': IconData(0xe2a4, fontFamily: 'MaterialIcons'),
  'folder_shared': IconData(0xe2a5, fontFamily: 'MaterialIcons'),
  'folder_special': IconData(0xe2a6, fontFamily: 'MaterialIcons'),
  'folder_zip': IconData(0xf0509, fontFamily: 'MaterialIcons'),
  'follow_the_signs': IconData(0xe2a7, fontFamily: 'MaterialIcons'),
  'font_download': IconData(0xe2a8, fontFamily: 'MaterialIcons'),
  'font_download_off': IconData(0xe2a9, fontFamily: 'MaterialIcons'),
  'food_bank': IconData(0xe2aa, fontFamily: 'MaterialIcons'),
  'forest': IconData(0xf050a, fontFamily: 'MaterialIcons'),
  'fork_left': IconData(0xf050b, fontFamily: 'MaterialIcons'),
  'fork_right': IconData(0xf050c, fontFamily: 'MaterialIcons'),
  'forklift': IconData(0xf0866, fontFamily: 'MaterialIcons'),
  'format_align_center': IconData(0xe2ab, fontFamily: 'MaterialIcons'),
  'format_align_justify': IconData(0xe2ac, fontFamily: 'MaterialIcons'),
  'format_align_left': IconData(0xe2ad, fontFamily: 'MaterialIcons'),
  'format_align_right': IconData(0xe2ae, fontFamily: 'MaterialIcons'),
  'format_bold': IconData(0xe2af, fontFamily: 'MaterialIcons'),
  'format_clear': IconData(0xe2b0, fontFamily: 'MaterialIcons'),
  'format_color_fill': IconData(0xe2b1, fontFamily: 'MaterialIcons'),
  'format_color_reset': IconData(0xe2b2, fontFamily: 'MaterialIcons'),
  'format_color_text': IconData(0xe2b3, fontFamily: 'MaterialIcons'),
  'format_indent_decrease':
      IconData(0xe2b4, fontFamily: 'MaterialIcons', matchTextDirection: true),
  'format_indent_increase':
      IconData(0xe2b5, fontFamily: 'MaterialIcons', matchTextDirection: true),
  'format_italic': IconData(0xe2b6, fontFamily: 'MaterialIcons'),
  'format_line_spacing': IconData(0xe2b7, fontFamily: 'MaterialIcons'),
  'format_list_bulleted':
      IconData(0xe2b8, fontFamily: 'MaterialIcons', matchTextDirection: true),
  'format_list_bulleted_add': IconData(0xf0867, fontFamily: 'MaterialIcons'),
  'format_list_numbered': IconData(0xe2b9, fontFamily: 'MaterialIcons'),
  'format_list_numbered_rtl': IconData(0xe2ba, fontFamily: 'MaterialIcons'),
  'format_overline': IconData(0xf050d, fontFamily: 'MaterialIcons'),
  'format_paint': IconData(0xe2bb, fontFamily: 'MaterialIcons'),
  'format_quote': IconData(0xe2bc, fontFamily: 'MaterialIcons'),
  'format_shapes': IconData(0xe2bd, fontFamily: 'MaterialIcons'),
  'format_size': IconData(0xe2be, fontFamily: 'MaterialIcons'),
  'format_strikethrough': IconData(0xe2bf, fontFamily: 'MaterialIcons'),
  'format_textdirection_l_to_r': IconData(0xe2c0, fontFamily: 'MaterialIcons'),
  'format_textdirection_r_to_l': IconData(0xe2c1, fontFamily: 'MaterialIcons'),
  'format_underline': IconData(0xe2c2, fontFamily: 'MaterialIcons'),
  'format_underlined': IconData(0xe2c2, fontFamily: 'MaterialIcons'),
  'fort': IconData(0xf050e, fontFamily: 'MaterialIcons'),
  'forum': IconData(0xe2c3, fontFamily: 'MaterialIcons'),
  'forward':
      IconData(0xe2c4, fontFamily: 'MaterialIcons', matchTextDirection: true),
  'forward_10': IconData(0xe2c5, fontFamily: 'MaterialIcons'),
  'forward_30': IconData(0xe2c6, fontFamily: 'MaterialIcons'),
  'forward_5': IconData(0xe2c7, fontFamily: 'MaterialIcons'),
  'forward_to_inbox': IconData(0xe2c8, fontFamily: 'MaterialIcons'),
  'foundation': IconData(0xe2c9, fontFamily: 'MaterialIcons'),
  'free_breakfast': IconData(0xe2ca, fontFamily: 'MaterialIcons'),
  'free_cancellation': IconData(0xf050f, fontFamily: 'MaterialIcons'),
  'front_hand': IconData(0xf0510, fontFamily: 'MaterialIcons'),
  'front_loader': IconData(0xf0868, fontFamily: 'MaterialIcons'),
  'fullscreen': IconData(0xe2cb, fontFamily: 'MaterialIcons'),
  'fullscreen_exit': IconData(0xe2cc, fontFamily: 'MaterialIcons'),
  'functions':
      IconData(0xe2cd, fontFamily: 'MaterialIcons', matchTextDirection: true),
  'g_mobiledata': IconData(0xe2ce, fontFamily: 'MaterialIcons'),
  'g_translate': IconData(0xe2cf, fontFamily: 'MaterialIcons'),
  'gamepad': IconData(0xe2d0, fontFamily: 'MaterialIcons'),
  'games': IconData(0xe2d1, fontFamily: 'MaterialIcons'),
  'garage': IconData(0xe2d2, fontFamily: 'MaterialIcons'),
  'gas_meter': IconData(0xf07a4, fontFamily: 'MaterialIcons'),
  'gavel': IconData(0xe2d3, fontFamily: 'MaterialIcons'),
  'generating_tokens': IconData(0xf0511, fontFamily: 'MaterialIcons'),
  'gesture': IconData(0xe2d4, fontFamily: 'MaterialIcons'),
  'get_app': IconData(0xe2d5, fontFamily: 'MaterialIcons'),
  'gif': IconData(0xe2d6, fontFamily: 'MaterialIcons'),
  'gif_box': IconData(0xf0512, fontFamily: 'MaterialIcons'),
  'girl': IconData(0xf0513, fontFamily: 'MaterialIcons'),
  'gite': IconData(0xe2d7, fontFamily: 'MaterialIcons'),
  'golf_course': IconData(0xe2d8, fontFamily: 'MaterialIcons'),
  'gpp_bad': IconData(0xe2d9, fontFamily: 'MaterialIcons'),
  'gpp_good': IconData(0xe2da, fontFamily: 'MaterialIcons'),
  'gpp_maybe': IconData(0xe2db, fontFamily: 'MaterialIcons'),
  'gps_fixed': IconData(0xe2dc, fontFamily: 'MaterialIcons'),
  'gps_not_fixed': IconData(0xe2dd, fontFamily: 'MaterialIcons'),
  'gps_off': IconData(0xe2de, fontFamily: 'MaterialIcons'),
  'grade': IconData(0xe2df, fontFamily: 'MaterialIcons'),
  'gradient': IconData(0xe2e0, fontFamily: 'MaterialIcons'),
  'grading': IconData(0xe2e1, fontFamily: 'MaterialIcons'),
  'grain': IconData(0xe2e2, fontFamily: 'MaterialIcons'),
  'graphic_eq': IconData(0xe2e3, fontFamily: 'MaterialIcons'),
  'grass': IconData(0xe2e4, fontFamily: 'MaterialIcons'),
  'grid_3x3': IconData(0xe2e5, fontFamily: 'MaterialIcons'),
  'grid_4x4': IconData(0xe2e6, fontFamily: 'MaterialIcons'),
  'grid_goldenratio': IconData(0xe2e7, fontFamily: 'MaterialIcons'),
  'grid_off': IconData(0xe2e8, fontFamily: 'MaterialIcons'),
  'grid_on': IconData(0xe2e9, fontFamily: 'MaterialIcons'),
  'grid_view': IconData(0xe2ea, fontFamily: 'MaterialIcons'),
  'group': IconData(0xe2eb, fontFamily: 'MaterialIcons'),
  'group_add': IconData(0xe2ec, fontFamily: 'MaterialIcons'),
  'group_off': IconData(0xf0514, fontFamily: 'MaterialIcons'),
  'group_remove': IconData(0xf0515, fontFamily: 'MaterialIcons'),
  'group_work': IconData(0xe2ed, fontFamily: 'MaterialIcons'),
  'groups': IconData(0xe2ee, fontFamily: 'MaterialIcons'),
  'groups_2': IconData(0xf0869, fontFamily: 'MaterialIcons'),
  'groups_3': IconData(0xf086a, fontFamily: 'MaterialIcons'),
  'h_mobiledata': IconData(0xe2ef, fontFamily: 'MaterialIcons'),
  'h_plus_mobiledata': IconData(0xe2f0, fontFamily: 'MaterialIcons'),
  'hail': IconData(0xe2f1, fontFamily: 'MaterialIcons'),
  'handshake': IconData(0xf06be, fontFamily: 'MaterialIcons'),
  'handyman': IconData(0xe2f2, fontFamily: 'MaterialIcons'),
  'hardware': IconData(0xe2f3, fontFamily: 'MaterialIcons'),
  'hd': IconData(0xe2f4, fontFamily: 'MaterialIcons'),
  'hdr_auto': IconData(0xe2f5, fontFamily: 'MaterialIcons'),
  'hdr_auto_select': IconData(0xe2f6, fontFamily: 'MaterialIcons'),
  'hdr_enhanced_select': IconData(0xe2f7, fontFamily: 'MaterialIcons'),
  'hdr_off': IconData(0xe2f8, fontFamily: 'MaterialIcons'),
  'hdr_off_select': IconData(0xe2f9, fontFamily: 'MaterialIcons'),
  'hdr_on': IconData(0xe2fa, fontFamily: 'MaterialIcons'),
  'hdr_on_select': IconData(0xe2fb, fontFamily: 'MaterialIcons'),
  'hdr_plus': IconData(0xe2fc, fontFamily: 'MaterialIcons'),
  'hdr_strong': IconData(0xe2fd, fontFamily: 'MaterialIcons'),
  'hdr_weak': IconData(0xe2fe, fontFamily: 'MaterialIcons'),
  'headphones': IconData(0xe2ff, fontFamily: 'MaterialIcons'),
  'headphones_battery': IconData(0xe300, fontFamily: 'MaterialIcons'),
  'headset': IconData(0xe301, fontFamily: 'MaterialIcons'),
  'headset_mic': IconData(0xe302, fontFamily: 'MaterialIcons'),
  'headset_off': IconData(0xe303, fontFamily: 'MaterialIcons'),
  'healing': IconData(0xe304, fontFamily: 'MaterialIcons'),
  'health_and_safety': IconData(0xe305, fontFamily: 'MaterialIcons'),
  'hearing': IconData(0xe306, fontFamily: 'MaterialIcons'),
  'hearing_disabled': IconData(0xe307, fontFamily: 'MaterialIcons'),
  'heart_broken': IconData(0xf0516, fontFamily: 'MaterialIcons'),
  'heat_pump': IconData(0xf07a5, fontFamily: 'MaterialIcons'),
  'height': IconData(0xe308, fontFamily: 'MaterialIcons'),
  'help':
      IconData(0xe309, fontFamily: 'MaterialIcons', matchTextDirection: true),
  'help_center': IconData(0xe30a, fontFamily: 'MaterialIcons'),
  'help_outline':
      IconData(0xe30b, fontFamily: 'MaterialIcons', matchTextDirection: true),
  'hevc': IconData(0xe30c, fontFamily: 'MaterialIcons'),
  'hexagon': IconData(0xf0517, fontFamily: 'MaterialIcons'),
  'hide_image': IconData(0xe30d, fontFamily: 'MaterialIcons'),
  'hide_source': IconData(0xe30e, fontFamily: 'MaterialIcons'),
  'high_quality': IconData(0xe30f, fontFamily: 'MaterialIcons'),
  'highlight': IconData(0xe310, fontFamily: 'MaterialIcons'),
  'highlight_alt': IconData(0xe311, fontFamily: 'MaterialIcons'),
  'highlight_off': IconData(0xe312, fontFamily: 'MaterialIcons'),
  'highlight_remove': IconData(0xe312, fontFamily: 'MaterialIcons'),
  'hiking': IconData(0xe313, fontFamily: 'MaterialIcons'),
  'history': IconData(0xe314, fontFamily: 'MaterialIcons'),
  'history_edu': IconData(0xe315, fontFamily: 'MaterialIcons'),
  'history_toggle_off': IconData(0xe316, fontFamily: 'MaterialIcons'),
  'hive': IconData(0xf0518, fontFamily: 'MaterialIcons'),
  'hls': IconData(0xf0519, fontFamily: 'MaterialIcons'),
  'hls_off': IconData(0xf051a, fontFamily: 'MaterialIcons'),
  'holiday_village': IconData(0xe317, fontFamily: 'MaterialIcons'),
  'home': IconData(0xe318, fontFamily: 'MaterialIcons'),
  'home_filled': IconData(0xe319, fontFamily: 'MaterialIcons'),
  'home_max': IconData(0xe31a, fontFamily: 'MaterialIcons'),
  'home_mini': IconData(0xe31b, fontFamily: 'MaterialIcons'),
  'home_repair_service': IconData(0xe31c, fontFamily: 'MaterialIcons'),
  'home_work': IconData(0xe31d, fontFamily: 'MaterialIcons'),
  'horizontal_distribute': IconData(0xe31e, fontFamily: 'MaterialIcons'),
  'horizontal_rule': IconData(0xe31f, fontFamily: 'MaterialIcons'),
  'horizontal_split': IconData(0xe320, fontFamily: 'MaterialIcons'),
  'hot_tub': IconData(0xe321, fontFamily: 'MaterialIcons'),
  'hotel': IconData(0xe322, fontFamily: 'MaterialIcons'),
  'hotel_class': IconData(0xf051b, fontFamily: 'MaterialIcons'),
  'hourglass_bottom': IconData(0xe323, fontFamily: 'MaterialIcons'),
  'hourglass_disabled': IconData(0xe324, fontFamily: 'MaterialIcons'),
  'hourglass_empty': IconData(0xe325, fontFamily: 'MaterialIcons'),
  'hourglass_full': IconData(0xe326, fontFamily: 'MaterialIcons'),
  'hourglass_top': IconData(0xe327, fontFamily: 'MaterialIcons'),
  'house': IconData(0xe328, fontFamily: 'MaterialIcons'),
  'house_siding': IconData(0xe329, fontFamily: 'MaterialIcons'),
  'houseboat': IconData(0xe32a, fontFamily: 'MaterialIcons'),
  'how_to_reg': IconData(0xe32b, fontFamily: 'MaterialIcons'),
  'how_to_vote': IconData(0xe32c, fontFamily: 'MaterialIcons'),
  'html': IconData(0xf051c, fontFamily: 'MaterialIcons'),
  'http': IconData(0xe32d, fontFamily: 'MaterialIcons'),
  'https': IconData(0xe32e, fontFamily: 'MaterialIcons'),
  'hub': IconData(0xf051d, fontFamily: 'MaterialIcons'),
  'hvac': IconData(0xe32f, fontFamily: 'MaterialIcons'),
  'ice_skating': IconData(0xe330, fontFamily: 'MaterialIcons'),
  'icecream': IconData(0xe331, fontFamily: 'MaterialIcons'),
  'image': IconData(0xe332, fontFamily: 'MaterialIcons'),
  'image_aspect_ratio': IconData(0xe333, fontFamily: 'MaterialIcons'),
  'image_not_supported': IconData(0xe334, fontFamily: 'MaterialIcons'),
  'image_search': IconData(0xe335, fontFamily: 'MaterialIcons'),
  'imagesearch_roller': IconData(0xe336, fontFamily: 'MaterialIcons'),
  'import_contacts': IconData(0xe337, fontFamily: 'MaterialIcons'),
  'import_export': IconData(0xe338, fontFamily: 'MaterialIcons'),
  'important_devices': IconData(0xe339, fontFamily: 'MaterialIcons'),
  'inbox': IconData(0xe33a, fontFamily: 'MaterialIcons'),
  'incomplete_circle': IconData(0xf051e, fontFamily: 'MaterialIcons'),
  'indeterminate_check_box': IconData(0xe33b, fontFamily: 'MaterialIcons'),
  'info': IconData(0xe33c, fontFamily: 'MaterialIcons'),
  'info_outline': IconData(0xe33d, fontFamily: 'MaterialIcons'),
  'input':
      IconData(0xe33e, fontFamily: 'MaterialIcons', matchTextDirection: true),
  'insert_chart': IconData(0xe33f, fontFamily: 'MaterialIcons'),
  'insert_comment': IconData(0xe341, fontFamily: 'MaterialIcons'),
  'insert_drive_file': IconData(0xe342, fontFamily: 'MaterialIcons'),
  'insert_emoticon': IconData(0xe343, fontFamily: 'MaterialIcons'),
  'insert_invitation': IconData(0xe344, fontFamily: 'MaterialIcons'),
  'insert_link': IconData(0xe345, fontFamily: 'MaterialIcons'),
  'insert_page_break': IconData(0xf0520, fontFamily: 'MaterialIcons'),
  'insert_photo': IconData(0xe346, fontFamily: 'MaterialIcons'),
  'insights': IconData(0xe347, fontFamily: 'MaterialIcons'),
  'install_desktop': IconData(0xf0521, fontFamily: 'MaterialIcons'),
  'install_mobile': IconData(0xf0522, fontFamily: 'MaterialIcons'),
  'integration_instructions': IconData(0xe348, fontFamily: 'MaterialIcons'),
  'interests': IconData(0xf0523, fontFamily: 'MaterialIcons'),
  'interpreter_mode': IconData(0xf0524, fontFamily: 'MaterialIcons'),
  'inventory': IconData(0xe349, fontFamily: 'MaterialIcons'),
  'inventory_2': IconData(0xe34a, fontFamily: 'MaterialIcons'),
  'invert_colors': IconData(0xe34b, fontFamily: 'MaterialIcons'),
  'invert_colors_off': IconData(0xe34c, fontFamily: 'MaterialIcons'),
  'invert_colors_on': IconData(0xe34b, fontFamily: 'MaterialIcons'),
  'ios_share': IconData(0xe34d, fontFamily: 'MaterialIcons'),
  'iron': IconData(0xe34e, fontFamily: 'MaterialIcons'),
  'iso': IconData(0xe34f, fontFamily: 'MaterialIcons'),
  'javascript': IconData(0xf0525, fontFamily: 'MaterialIcons'),
  'join_full': IconData(0xf0526, fontFamily: 'MaterialIcons'),
  'join_inner': IconData(0xf0527, fontFamily: 'MaterialIcons'),
  'join_left': IconData(0xf0528, fontFamily: 'MaterialIcons'),
  'join_right': IconData(0xf0529, fontFamily: 'MaterialIcons'),
  'kayaking': IconData(0xe350, fontFamily: 'MaterialIcons'),
  'kebab_dining': IconData(0xf052a, fontFamily: 'MaterialIcons'),
  'key': IconData(0xf052b, fontFamily: 'MaterialIcons'),
  'key_off': IconData(0xf052c, fontFamily: 'MaterialIcons'),
  'keyboard': IconData(0xe351, fontFamily: 'MaterialIcons'),
  'keyboard_alt': IconData(0xe352, fontFamily: 'MaterialIcons'),
  'keyboard_arrow_down': IconData(0xe353, fontFamily: 'MaterialIcons'),
  'keyboard_arrow_left': IconData(0xe354, fontFamily: 'MaterialIcons'),
  'keyboard_arrow_right': IconData(0xe355, fontFamily: 'MaterialIcons'),
  'keyboard_arrow_up': IconData(0xe356, fontFamily: 'MaterialIcons'),
  'keyboard_backspace':
      IconData(0xe357, fontFamily: 'MaterialIcons', matchTextDirection: true),
  'keyboard_capslock': IconData(0xe358, fontFamily: 'MaterialIcons'),
  'keyboard_command_key': IconData(0xf052d, fontFamily: 'MaterialIcons'),
  'keyboard_control': IconData(0xe402, fontFamily: 'MaterialIcons'),
  'keyboard_control_key': IconData(0xf052e, fontFamily: 'MaterialIcons'),
  'keyboard_double_arrow_down': IconData(0xf052f, fontFamily: 'MaterialIcons'),
  'keyboard_double_arrow_left': IconData(0xf0530, fontFamily: 'MaterialIcons'),
  'keyboard_double_arrow_right': IconData(0xf0531, fontFamily: 'MaterialIcons'),
  'keyboard_double_arrow_up': IconData(0xf0532, fontFamily: 'MaterialIcons'),
  'keyboard_hide': IconData(0xe359, fontFamily: 'MaterialIcons'),
  'keyboard_option_key': IconData(0xf0533, fontFamily: 'MaterialIcons'),
  'keyboard_return': IconData(0xe35a, fontFamily: 'MaterialIcons'),
  'keyboard_tab':
      IconData(0xe35b, fontFamily: 'MaterialIcons', matchTextDirection: true),
  'keyboard_voice': IconData(0xe35c, fontFamily: 'MaterialIcons'),
  'king_bed': IconData(0xe35d, fontFamily: 'MaterialIcons'),
  'kitchen': IconData(0xe35e, fontFamily: 'MaterialIcons'),
  'kitesurfing': IconData(0xe35f, fontFamily: 'MaterialIcons'),
  'label':
      IconData(0xe360, fontFamily: 'MaterialIcons', matchTextDirection: true),
  'label_important':
      IconData(0xe361, fontFamily: 'MaterialIcons', matchTextDirection: true),
  'label_important_outline': IconData(0xe362, fontFamily: 'MaterialIcons'),
  'label_off':
      IconData(0xe363, fontFamily: 'MaterialIcons', matchTextDirection: true),
  'label_outline':
      IconData(0xe364, fontFamily: 'MaterialIcons', matchTextDirection: true),
  'lan': IconData(0xf0534, fontFamily: 'MaterialIcons'),
  'landscape': IconData(0xe365, fontFamily: 'MaterialIcons'),
  'landslide': IconData(0xf07a6, fontFamily: 'MaterialIcons'),
  'language': IconData(0xe366, fontFamily: 'MaterialIcons'),
  'laptop': IconData(0xe367, fontFamily: 'MaterialIcons'),
  'laptop_chromebook': IconData(0xe368, fontFamily: 'MaterialIcons'),
  'laptop_mac': IconData(0xe369, fontFamily: 'MaterialIcons'),
  'laptop_windows': IconData(0xe36a, fontFamily: 'MaterialIcons'),
  'last_page':
      IconData(0xe36b, fontFamily: 'MaterialIcons', matchTextDirection: true),
  'launch':
      IconData(0xe36c, fontFamily: 'MaterialIcons', matchTextDirection: true),
  'layers': IconData(0xe36d, fontFamily: 'MaterialIcons'),
  'layers_clear': IconData(0xe36e, fontFamily: 'MaterialIcons'),
  'leaderboard': IconData(0xe36f, fontFamily: 'MaterialIcons'),
  'leak_add': IconData(0xe370, fontFamily: 'MaterialIcons'),
  'leak_remove': IconData(0xe371, fontFamily: 'MaterialIcons'),
  'leave_bags_at_home': IconData(0xe439, fontFamily: 'MaterialIcons'),
  'legend_toggle': IconData(0xe372, fontFamily: 'MaterialIcons'),
  'lens': IconData(0xe373, fontFamily: 'MaterialIcons'),
  'lens_blur': IconData(0xe374, fontFamily: 'MaterialIcons'),
  'library_add': IconData(0xe375, fontFamily: 'MaterialIcons'),
  'library_add_check': IconData(0xe376, fontFamily: 'MaterialIcons'),
  'library_books': IconData(0xe377, fontFamily: 'MaterialIcons'),
  'library_music': IconData(0xe378, fontFamily: 'MaterialIcons'),
  'light': IconData(0xe379, fontFamily: 'MaterialIcons'),
  'light_mode': IconData(0xe37a, fontFamily: 'MaterialIcons'),
  'lightbulb': IconData(0xe37b, fontFamily: 'MaterialIcons'),
  'lightbulb_circle': IconData(0xf07a7, fontFamily: 'MaterialIcons'),
  'lightbulb_outline': IconData(0xe37c, fontFamily: 'MaterialIcons'),
  'line_axis': IconData(0xf0535, fontFamily: 'MaterialIcons'),
  'line_style': IconData(0xe37d, fontFamily: 'MaterialIcons'),
  'line_weight': IconData(0xe37e, fontFamily: 'MaterialIcons'),
  'linear_scale': IconData(0xe37f, fontFamily: 'MaterialIcons'),
  'link': IconData(0xe380, fontFamily: 'MaterialIcons'),
  'link_off': IconData(0xe381, fontFamily: 'MaterialIcons'),
  'linked_camera': IconData(0xe382, fontFamily: 'MaterialIcons'),
  'liquor': IconData(0xe383, fontFamily: 'MaterialIcons'),
  'list':
      IconData(0xe384, fontFamily: 'MaterialIcons', matchTextDirection: true),
  'list_alt':
      IconData(0xe385, fontFamily: 'MaterialIcons', matchTextDirection: true),
  'live_help':
      IconData(0xe386, fontFamily: 'MaterialIcons', matchTextDirection: true),
  'live_tv': IconData(0xe387, fontFamily: 'MaterialIcons'),
  'living': IconData(0xe388, fontFamily: 'MaterialIcons'),
  'local_activity': IconData(0xe389, fontFamily: 'MaterialIcons'),
  'local_airport': IconData(0xe38a, fontFamily: 'MaterialIcons'),
  'local_atm': IconData(0xe38b, fontFamily: 'MaterialIcons'),
  'local_attraction': IconData(0xe389, fontFamily: 'MaterialIcons'),
  'local_bar': IconData(0xe38c, fontFamily: 'MaterialIcons'),
  'local_cafe': IconData(0xe38d, fontFamily: 'MaterialIcons'),
  'local_car_wash': IconData(0xe38e, fontFamily: 'MaterialIcons'),
  'local_convenience_store': IconData(0xe38f, fontFamily: 'MaterialIcons'),
  'local_dining': IconData(0xe390, fontFamily: 'MaterialIcons'),
  'local_drink': IconData(0xe391, fontFamily: 'MaterialIcons'),
  'local_fire_department': IconData(0xe392, fontFamily: 'MaterialIcons'),
  'local_florist': IconData(0xe393, fontFamily: 'MaterialIcons'),
  'local_gas_station': IconData(0xe394, fontFamily: 'MaterialIcons'),
  'local_grocery_store': IconData(0xe395, fontFamily: 'MaterialIcons'),
  'local_hospital': IconData(0xe396, fontFamily: 'MaterialIcons'),
  'local_hotel': IconData(0xe397, fontFamily: 'MaterialIcons'),
  'local_laundry_service': IconData(0xe398, fontFamily: 'MaterialIcons'),
  'local_library': IconData(0xe399, fontFamily: 'MaterialIcons'),
  'local_mall': IconData(0xe39a, fontFamily: 'MaterialIcons'),
  'local_movies': IconData(0xe39b, fontFamily: 'MaterialIcons'),
  'local_offer': IconData(0xe39c, fontFamily: 'MaterialIcons'),
  'local_parking': IconData(0xe39d, fontFamily: 'MaterialIcons'),
  'local_pharmacy': IconData(0xe39e, fontFamily: 'MaterialIcons'),
  'local_phone': IconData(0xe39f, fontFamily: 'MaterialIcons'),
  'local_pizza': IconData(0xe3a0, fontFamily: 'MaterialIcons'),
  'local_play': IconData(0xe3a1, fontFamily: 'MaterialIcons'),
  'local_police': IconData(0xe3a2, fontFamily: 'MaterialIcons'),
  'local_post_office': IconData(0xe3a3, fontFamily: 'MaterialIcons'),
  'local_print_shop': IconData(0xe3a4, fontFamily: 'MaterialIcons'),
  'local_printshop': IconData(0xe3a4, fontFamily: 'MaterialIcons'),
  'local_restaurant': IconData(0xe390, fontFamily: 'MaterialIcons'),
  'local_see': IconData(0xe3a5, fontFamily: 'MaterialIcons'),
  'local_shipping': IconData(0xe3a6, fontFamily: 'MaterialIcons'),
  'local_taxi': IconData(0xe3a7, fontFamily: 'MaterialIcons'),
  'location_city': IconData(0xe3a8, fontFamily: 'MaterialIcons'),
  'location_disabled': IconData(0xe3a9, fontFamily: 'MaterialIcons'),
  'location_history': IconData(0xe498, fontFamily: 'MaterialIcons'),
  'location_off': IconData(0xe3aa, fontFamily: 'MaterialIcons'),
  'location_on': IconData(0xe3ab, fontFamily: 'MaterialIcons'),
  'location_pin': IconData(0xe3ac, fontFamily: 'MaterialIcons'),
  'location_searching': IconData(0xe3ad, fontFamily: 'MaterialIcons'),
  'lock': IconData(0xe3ae, fontFamily: 'MaterialIcons'),
  'lock_clock': IconData(0xe3af, fontFamily: 'MaterialIcons'),
  'lock_open': IconData(0xe3b0, fontFamily: 'MaterialIcons'),
  'lock_outline': IconData(0xe3b1, fontFamily: 'MaterialIcons'),
  'lock_person': IconData(0xf07a8, fontFamily: 'MaterialIcons'),
  'lock_reset': IconData(0xf0536, fontFamily: 'MaterialIcons'),
  'login': IconData(0xe3b2, fontFamily: 'MaterialIcons'),
  'logo_dev': IconData(0xf0537, fontFamily: 'MaterialIcons'),
  'logout': IconData(0xe3b3, fontFamily: 'MaterialIcons'),
  'looks': IconData(0xe3b4, fontFamily: 'MaterialIcons'),
  'looks_3': IconData(0xe3b5, fontFamily: 'MaterialIcons'),
  'looks_4': IconData(0xe3b6, fontFamily: 'MaterialIcons'),
  'looks_5': IconData(0xe3b7, fontFamily: 'MaterialIcons'),
  'looks_6': IconData(0xe3b8, fontFamily: 'MaterialIcons'),
  'looks_one': IconData(0xe3b9, fontFamily: 'MaterialIcons'),
  'looks_two': IconData(0xe3ba, fontFamily: 'MaterialIcons'),
  'loop': IconData(0xe3bb, fontFamily: 'MaterialIcons'),
  'loupe': IconData(0xe3bc, fontFamily: 'MaterialIcons'),
  'low_priority': IconData(0xe3bd, fontFamily: 'MaterialIcons'),
  'loyalty': IconData(0xe3be, fontFamily: 'MaterialIcons'),
  'lte_mobiledata': IconData(0xe3bf, fontFamily: 'MaterialIcons'),
  'lte_plus_mobiledata': IconData(0xe3c0, fontFamily: 'MaterialIcons'),
  'luggage': IconData(0xe3c1, fontFamily: 'MaterialIcons'),
  'lunch_dining': IconData(0xe3c2, fontFamily: 'MaterialIcons'),
  'lyrics': IconData(0xf07a9, fontFamily: 'MaterialIcons'),
  'macro_off': IconData(0xf086b, fontFamily: 'MaterialIcons'),
  'mail': IconData(0xe3c3, fontFamily: 'MaterialIcons'),
  'mail_lock': IconData(0xf07aa, fontFamily: 'MaterialIcons'),
  'mail_outline': IconData(0xe3c4, fontFamily: 'MaterialIcons'),
  'male': IconData(0xe3c5, fontFamily: 'MaterialIcons'),
  'man': IconData(0xf0538, fontFamily: 'MaterialIcons'),
  'man_2': IconData(0xf086c, fontFamily: 'MaterialIcons'),
  'man_3': IconData(0xf086d, fontFamily: 'MaterialIcons'),
  'man_4': IconData(0xf086e, fontFamily: 'MaterialIcons'),
  'manage_accounts': IconData(0xe3c6, fontFamily: 'MaterialIcons'),
  'manage_history': IconData(0xf07ab, fontFamily: 'MaterialIcons'),
  'manage_search': IconData(0xe3c7, fontFamily: 'MaterialIcons'),
  'map': IconData(0xe3c8, fontFamily: 'MaterialIcons'),
  'maps_home_work': IconData(0xe3c9, fontFamily: 'MaterialIcons'),
  'maps_ugc': IconData(0xe3ca, fontFamily: 'MaterialIcons'),
  'margin': IconData(0xe3cb, fontFamily: 'MaterialIcons'),
  'mark_as_unread': IconData(0xe3cc, fontFamily: 'MaterialIcons'),
  'mark_chat_read': IconData(0xe3cd, fontFamily: 'MaterialIcons'),
  'mark_chat_unread': IconData(0xe3ce, fontFamily: 'MaterialIcons'),
  'mark_email_read': IconData(0xe3cf, fontFamily: 'MaterialIcons'),
  'mark_email_unread': IconData(0xe3d0, fontFamily: 'MaterialIcons'),
  'mark_unread_chat_alt': IconData(0xf0539, fontFamily: 'MaterialIcons'),
  'markunread': IconData(0xe3d1, fontFamily: 'MaterialIcons'),
  'markunread_mailbox': IconData(0xe3d2, fontFamily: 'MaterialIcons'),
  'masks': IconData(0xe3d3, fontFamily: 'MaterialIcons'),
  'maximize': IconData(0xe3d4, fontFamily: 'MaterialIcons'),
  'media_bluetooth_off': IconData(0xe3d5, fontFamily: 'MaterialIcons'),
  'media_bluetooth_on': IconData(0xe3d6, fontFamily: 'MaterialIcons'),
  'mediation': IconData(0xe3d7, fontFamily: 'MaterialIcons'),
  'medical_information': IconData(0xf07ac, fontFamily: 'MaterialIcons'),
  'medical_services': IconData(0xe3d8, fontFamily: 'MaterialIcons'),
  'medication': IconData(0xe3d9, fontFamily: 'MaterialIcons'),
  'medication_liquid': IconData(0xf053a, fontFamily: 'MaterialIcons'),
  'meeting_room': IconData(0xe3da, fontFamily: 'MaterialIcons'),
  'memory': IconData(0xe3db, fontFamily: 'MaterialIcons'),
  'menu': IconData(0xe3dc, fontFamily: 'MaterialIcons'),
  'menu_book': IconData(0xe3dd, fontFamily: 'MaterialIcons'),
  'menu_open': IconData(0xe3de, fontFamily: 'MaterialIcons'),
  'merge': IconData(0xf053b, fontFamily: 'MaterialIcons'),
  'merge_type': IconData(0xe3df, fontFamily: 'MaterialIcons'),
  'message': IconData(0xe3e0, fontFamily: 'MaterialIcons'),
  'messenger': IconData(0xe154, fontFamily: 'MaterialIcons'),
  'messenger_outline': IconData(0xe155, fontFamily: 'MaterialIcons'),
  'mic': IconData(0xe3e1, fontFamily: 'MaterialIcons'),
  'mic_external_off': IconData(0xe3e2, fontFamily: 'MaterialIcons'),
  'mic_external_on': IconData(0xe3e3, fontFamily: 'MaterialIcons'),
  'mic_none': IconData(0xe3e4, fontFamily: 'MaterialIcons'),
  'mic_off': IconData(0xe3e5, fontFamily: 'MaterialIcons'),
  'microwave': IconData(0xe3e6, fontFamily: 'MaterialIcons'),
  'military_tech': IconData(0xe3e7, fontFamily: 'MaterialIcons'),
  'minimize': IconData(0xe3e8, fontFamily: 'MaterialIcons'),
  'minor_crash': IconData(0xf07ad, fontFamily: 'MaterialIcons'),
  'miscellaneous_services': IconData(0xe3e9, fontFamily: 'MaterialIcons'),
  'missed_video_call': IconData(0xe3ea, fontFamily: 'MaterialIcons'),
  'mms': IconData(0xe3eb, fontFamily: 'MaterialIcons'),
  'mobile_friendly': IconData(0xe3ec, fontFamily: 'MaterialIcons'),
  'mobile_off': IconData(0xe3ed, fontFamily: 'MaterialIcons'),
  'mobile_screen_share':
      IconData(0xe3ee, fontFamily: 'MaterialIcons', matchTextDirection: true),
  'mobiledata_off': IconData(0xe3ef, fontFamily: 'MaterialIcons'),
  'mode': IconData(0xe3f0, fontFamily: 'MaterialIcons'),
  'mode_comment': IconData(0xe3f1, fontFamily: 'MaterialIcons'),
  'mode_edit': IconData(0xe3f2, fontFamily: 'MaterialIcons'),
  'mode_edit_outline': IconData(0xe3f3, fontFamily: 'MaterialIcons'),
  'mode_fan_off': IconData(0xf07ae, fontFamily: 'MaterialIcons'),
  'mode_night': IconData(0xe3f4, fontFamily: 'MaterialIcons'),
  'mode_of_travel': IconData(0xf053c, fontFamily: 'MaterialIcons'),
  'mode_standby': IconData(0xe3f5, fontFamily: 'MaterialIcons'),
  'model_training': IconData(0xe3f6, fontFamily: 'MaterialIcons'),
  'monetization_on': IconData(0xe3f7, fontFamily: 'MaterialIcons'),
  'money': IconData(0xe3f8, fontFamily: 'MaterialIcons'),
  'money_off': IconData(0xe3f9, fontFamily: 'MaterialIcons'),
  'money_off_csred': IconData(0xe3fa, fontFamily: 'MaterialIcons'),
  'monitor': IconData(0xe3fb, fontFamily: 'MaterialIcons'),
  'monitor_heart': IconData(0xf053d, fontFamily: 'MaterialIcons'),
  'monitor_weight': IconData(0xe3fc, fontFamily: 'MaterialIcons'),
  'monochrome_photos': IconData(0xe3fd, fontFamily: 'MaterialIcons'),
  'mood': IconData(0xe3fe, fontFamily: 'MaterialIcons'),
  'mood_bad': IconData(0xe3ff, fontFamily: 'MaterialIcons'),
  'moped': IconData(0xe400, fontFamily: 'MaterialIcons'),
  'more': IconData(0xe401, fontFamily: 'MaterialIcons'),
  'more_horiz': IconData(0xe402, fontFamily: 'MaterialIcons'),
  'more_time': IconData(0xe403, fontFamily: 'MaterialIcons'),
  'more_vert': IconData(0xe404, fontFamily: 'MaterialIcons'),
  'mosque': IconData(0xf053e, fontFamily: 'MaterialIcons'),
  'motion_photos_auto': IconData(0xe405, fontFamily: 'MaterialIcons'),
  'motion_photos_off': IconData(0xe406, fontFamily: 'MaterialIcons'),
  'motion_photos_on': IconData(0xe407, fontFamily: 'MaterialIcons'),
  'motion_photos_pause': IconData(0xe408, fontFamily: 'MaterialIcons'),
  'motion_photos_paused': IconData(0xe409, fontFamily: 'MaterialIcons'),
  'motorcycle': IconData(0xe40a, fontFamily: 'MaterialIcons'),
  'mouse': IconData(0xe40b, fontFamily: 'MaterialIcons'),
  'move_down': IconData(0xf053f, fontFamily: 'MaterialIcons'),
  'move_to_inbox': IconData(0xe40c, fontFamily: 'MaterialIcons'),
  'move_up': IconData(0xf0540, fontFamily: 'MaterialIcons'),
  'movie': IconData(0xe40d, fontFamily: 'MaterialIcons'),
  'movie_creation': IconData(0xe40e, fontFamily: 'MaterialIcons'),
  'movie_edit': IconData(0xf08b9, fontFamily: 'MaterialIcons'),
  'movie_filter': IconData(0xe40f, fontFamily: 'MaterialIcons'),
  'moving': IconData(0xe410, fontFamily: 'MaterialIcons'),
  'mp': IconData(0xe411, fontFamily: 'MaterialIcons'),
  'multiline_chart':
      IconData(0xe412, fontFamily: 'MaterialIcons', matchTextDirection: true),
  'multiple_stop': IconData(0xe413, fontFamily: 'MaterialIcons'),
  'multitrack_audio': IconData(0xe2e3, fontFamily: 'MaterialIcons'),
  'museum': IconData(0xe414, fontFamily: 'MaterialIcons'),
  'music_note': IconData(0xe415, fontFamily: 'MaterialIcons'),
  'music_off': IconData(0xe416, fontFamily: 'MaterialIcons'),
  'music_video': IconData(0xe417, fontFamily: 'MaterialIcons'),
  'my_library_add': IconData(0xe375, fontFamily: 'MaterialIcons'),
  'my_library_books': IconData(0xe377, fontFamily: 'MaterialIcons'),
  'my_library_music': IconData(0xe378, fontFamily: 'MaterialIcons'),
  'my_location': IconData(0xe418, fontFamily: 'MaterialIcons'),
  'nat': IconData(0xe419, fontFamily: 'MaterialIcons'),
  'nature': IconData(0xe41a, fontFamily: 'MaterialIcons'),
  'nature_people': IconData(0xe41b, fontFamily: 'MaterialIcons'),
  'navigate_before':
      IconData(0xe41c, fontFamily: 'MaterialIcons', matchTextDirection: true),
  'navigate_next':
      IconData(0xe41d, fontFamily: 'MaterialIcons', matchTextDirection: true),
  'navigation': IconData(0xe41e, fontFamily: 'MaterialIcons'),
  'near_me': IconData(0xe41f, fontFamily: 'MaterialIcons'),
  'near_me_disabled': IconData(0xe420, fontFamily: 'MaterialIcons'),
  'nearby_error': IconData(0xe421, fontFamily: 'MaterialIcons'),
  'nearby_off': IconData(0xe422, fontFamily: 'MaterialIcons'),
  'nest_cam_wired_stand': IconData(0xf07af, fontFamily: 'MaterialIcons'),
  'network_cell': IconData(0xe423, fontFamily: 'MaterialIcons'),
  'network_check': IconData(0xe424, fontFamily: 'MaterialIcons'),
  'network_locked': IconData(0xe425, fontFamily: 'MaterialIcons'),
  'network_ping': IconData(0xf06bf, fontFamily: 'MaterialIcons'),
  'network_wifi': IconData(0xe426, fontFamily: 'MaterialIcons'),
  'network_wifi_1_bar': IconData(0xf07b0, fontFamily: 'MaterialIcons'),
  'network_wifi_2_bar': IconData(0xf07b1, fontFamily: 'MaterialIcons'),
  'network_wifi_3_bar': IconData(0xf07b2, fontFamily: 'MaterialIcons'),
  'new_label': IconData(0xe427, fontFamily: 'MaterialIcons'),
  'new_releases': IconData(0xe428, fontFamily: 'MaterialIcons'),
  'newspaper': IconData(0xf0541, fontFamily: 'MaterialIcons'),
  'next_plan': IconData(0xe429, fontFamily: 'MaterialIcons'),
  'next_week':
      IconData(0xe42a, fontFamily: 'MaterialIcons', matchTextDirection: true),
  'nfc': IconData(0xe42b, fontFamily: 'MaterialIcons'),
  'night_shelter': IconData(0xe42c, fontFamily: 'MaterialIcons'),
  'nightlife': IconData(0xe42d, fontFamily: 'MaterialIcons'),
  'nightlight': IconData(0xe42e, fontFamily: 'MaterialIcons'),
  'nights_stay': IconData(0xe430, fontFamily: 'MaterialIcons'),
  'no_accounts': IconData(0xe431, fontFamily: 'MaterialIcons'),
  'no_adult_content': IconData(0xf07b3, fontFamily: 'MaterialIcons'),
  'no_backpack': IconData(0xe432, fontFamily: 'MaterialIcons'),
  'no_cell': IconData(0xe433, fontFamily: 'MaterialIcons'),
  'no_crash': IconData(0xf07b4, fontFamily: 'MaterialIcons'),
  'no_drinks': IconData(0xe434, fontFamily: 'MaterialIcons'),
  'no_encryption': IconData(0xe435, fontFamily: 'MaterialIcons'),
  'no_encryption_gmailerrorred': IconData(0xe436, fontFamily: 'MaterialIcons'),
  'no_flash': IconData(0xe437, fontFamily: 'MaterialIcons'),
  'no_food': IconData(0xe438, fontFamily: 'MaterialIcons'),
  'no_luggage': IconData(0xe439, fontFamily: 'MaterialIcons'),
  'no_meals': IconData(0xe43a, fontFamily: 'MaterialIcons'),
  'no_meals_ouline': IconData(0xe43b, fontFamily: 'MaterialIcons'),
  'no_meeting_room': IconData(0xe43c, fontFamily: 'MaterialIcons'),
  'no_photography': IconData(0xe43d, fontFamily: 'MaterialIcons'),
  'no_sim': IconData(0xe43e, fontFamily: 'MaterialIcons'),
  'no_stroller': IconData(0xe43f, fontFamily: 'MaterialIcons'),
  'no_transfer': IconData(0xe440, fontFamily: 'MaterialIcons'),
  'noise_aware': IconData(0xf07b5, fontFamily: 'MaterialIcons'),
  'noise_control_off': IconData(0xf07b6, fontFamily: 'MaterialIcons'),
  'nordic_walking': IconData(0xe441, fontFamily: 'MaterialIcons'),
  'north': IconData(0xe442, fontFamily: 'MaterialIcons'),
  'north_east': IconData(0xe443, fontFamily: 'MaterialIcons'),
  'north_west': IconData(0xe444, fontFamily: 'MaterialIcons'),
  'not_accessible': IconData(0xe445, fontFamily: 'MaterialIcons'),
  'not_interested': IconData(0xe446, fontFamily: 'MaterialIcons'),
  'not_listed_location': IconData(0xe447, fontFamily: 'MaterialIcons'),
  'not_started': IconData(0xe448, fontFamily: 'MaterialIcons'),
  'note':
      IconData(0xe449, fontFamily: 'MaterialIcons', matchTextDirection: true),
  'note_add': IconData(0xe44a, fontFamily: 'MaterialIcons'),
  'note_alt':
      IconData(0xe44b, fontFamily: 'MaterialIcons', matchTextDirection: true),
  'notes': IconData(0xe44c, fontFamily: 'MaterialIcons'),
  'notification_add': IconData(0xe44d, fontFamily: 'MaterialIcons'),
  'notification_important': IconData(0xe44e, fontFamily: 'MaterialIcons'),
  'notifications': IconData(0xe44f, fontFamily: 'MaterialIcons'),
  'notifications_active': IconData(0xe450, fontFamily: 'MaterialIcons'),
  'notifications_none': IconData(0xe451, fontFamily: 'MaterialIcons'),
  'notifications_off': IconData(0xe452, fontFamily: 'MaterialIcons'),
  'notifications_on': IconData(0xe450, fontFamily: 'MaterialIcons'),
  'notifications_paused': IconData(0xe453, fontFamily: 'MaterialIcons'),
  'now_wallpaper': IconData(0xe6ca, fontFamily: 'MaterialIcons'),
  'now_widgets': IconData(0xe6e6, fontFamily: 'MaterialIcons'),
  'numbers': IconData(0xf0542, fontFamily: 'MaterialIcons'),
  'offline_bolt': IconData(0xe454, fontFamily: 'MaterialIcons'),
  'offline_pin': IconData(0xe455, fontFamily: 'MaterialIcons'),
  'offline_share': IconData(0xe456, fontFamily: 'MaterialIcons'),
  'oil_barrel': IconData(0xf07b7, fontFamily: 'MaterialIcons'),
  'on_device_training': IconData(0xf07b8, fontFamily: 'MaterialIcons'),
  'ondemand_video': IconData(0xe457, fontFamily: 'MaterialIcons'),
  'online_prediction': IconData(0xe458, fontFamily: 'MaterialIcons'),
  'opacity': IconData(0xe459, fontFamily: 'MaterialIcons'),
  'open_in_browser': IconData(0xe45a, fontFamily: 'MaterialIcons'),
  'open_in_full': IconData(0xe45b, fontFamily: 'MaterialIcons'),
  'open_in_new':
      IconData(0xe45c, fontFamily: 'MaterialIcons', matchTextDirection: true),
  'open_in_new_off': IconData(0xe45d, fontFamily: 'MaterialIcons'),
  'open_with': IconData(0xe45e, fontFamily: 'MaterialIcons'),
  'other_houses': IconData(0xe45f, fontFamily: 'MaterialIcons'),
  'outbond': IconData(0xe460, fontFamily: 'MaterialIcons'),
  'outbound': IconData(0xe461, fontFamily: 'MaterialIcons'),
  'outbox': IconData(0xe462, fontFamily: 'MaterialIcons'),
  'outdoor_grill': IconData(0xe463, fontFamily: 'MaterialIcons'),
  'outgoing_mail': IconData(0xe464, fontFamily: 'MaterialIcons'),
  'outlet': IconData(0xe465, fontFamily: 'MaterialIcons'),
  'output': IconData(0xf0543, fontFamily: 'MaterialIcons'),
  'padding': IconData(0xe467, fontFamily: 'MaterialIcons'),
  'pages': IconData(0xe468, fontFamily: 'MaterialIcons'),
  'pageview': IconData(0xe469, fontFamily: 'MaterialIcons'),
  'paid': IconData(0xe46a, fontFamily: 'MaterialIcons'),
  'palette': IconData(0xe46b, fontFamily: 'MaterialIcons'),
  'pallet': IconData(0xf086f, fontFamily: 'MaterialIcons'),
  'pan_tool': IconData(0xe46c, fontFamily: 'MaterialIcons'),
  'pan_tool_alt': IconData(0xf0544, fontFamily: 'MaterialIcons'),
  'panorama': IconData(0xe46d, fontFamily: 'MaterialIcons'),
  'panorama_fish_eye': IconData(0xe46e, fontFamily: 'MaterialIcons'),
  'panorama_fisheye': IconData(0xe46e, fontFamily: 'MaterialIcons'),
  'panorama_horizontal': IconData(0xe46f, fontFamily: 'MaterialIcons'),
  'panorama_horizontal_select': IconData(0xe470, fontFamily: 'MaterialIcons'),
  'panorama_photosphere': IconData(0xe471, fontFamily: 'MaterialIcons'),
  'panorama_photosphere_select': IconData(0xe472, fontFamily: 'MaterialIcons'),
  'panorama_vertical': IconData(0xe473, fontFamily: 'MaterialIcons'),
  'panorama_vertical_select': IconData(0xe474, fontFamily: 'MaterialIcons'),
  'panorama_wide_angle': IconData(0xe475, fontFamily: 'MaterialIcons'),
  'panorama_wide_angle_select': IconData(0xe476, fontFamily: 'MaterialIcons'),
  'paragliding': IconData(0xe477, fontFamily: 'MaterialIcons'),
  'park': IconData(0xe478, fontFamily: 'MaterialIcons'),
  'party_mode': IconData(0xe479, fontFamily: 'MaterialIcons'),
  'password': IconData(0xe47a, fontFamily: 'MaterialIcons'),
  'paste': IconData(0xe192, fontFamily: 'MaterialIcons'),
  'pattern': IconData(0xe47b, fontFamily: 'MaterialIcons'),
  'pause': IconData(0xe47c, fontFamily: 'MaterialIcons'),
  'pause_circle': IconData(0xe47d, fontFamily: 'MaterialIcons'),
  'pause_circle_filled': IconData(0xe47e, fontFamily: 'MaterialIcons'),
  'pause_circle_outline': IconData(0xe47f, fontFamily: 'MaterialIcons'),
  'pause_presentation': IconData(0xe480, fontFamily: 'MaterialIcons'),
  'payment': IconData(0xe481, fontFamily: 'MaterialIcons'),
  'payments': IconData(0xe482, fontFamily: 'MaterialIcons'),
  'paypal': IconData(0xf0545, fontFamily: 'MaterialIcons'),
  'pedal_bike': IconData(0xe483, fontFamily: 'MaterialIcons'),
  'pending': IconData(0xe484, fontFamily: 'MaterialIcons'),
  'pending_actions': IconData(0xe485, fontFamily: 'MaterialIcons'),
  'pentagon': IconData(0xf0546, fontFamily: 'MaterialIcons'),
  'people': IconData(0xe486, fontFamily: 'MaterialIcons'),
  'people_alt': IconData(0xe487, fontFamily: 'MaterialIcons'),
  'people_outline': IconData(0xe488, fontFamily: 'MaterialIcons'),
  'percent': IconData(0xf0547, fontFamily: 'MaterialIcons'),
  'perm_camera_mic': IconData(0xe489, fontFamily: 'MaterialIcons'),
  'perm_contact_cal': IconData(0xe48a, fontFamily: 'MaterialIcons'),
  'perm_contact_calendar': IconData(0xe48a, fontFamily: 'MaterialIcons'),
  'perm_data_setting': IconData(0xe48b, fontFamily: 'MaterialIcons'),
  'perm_device_info': IconData(0xe48c, fontFamily: 'MaterialIcons'),
  'perm_device_information': IconData(0xe48c, fontFamily: 'MaterialIcons'),
  'perm_identity': IconData(0xe48d, fontFamily: 'MaterialIcons'),
  'perm_media': IconData(0xe48e, fontFamily: 'MaterialIcons'),
  'perm_phone_msg': IconData(0xe48f, fontFamily: 'MaterialIcons'),
  'perm_scan_wifi': IconData(0xe490, fontFamily: 'MaterialIcons'),
  'person': IconData(0xe491, fontFamily: 'MaterialIcons'),
  'person_2': IconData(0xf0870, fontFamily: 'MaterialIcons'),
  'person_3': IconData(0xf0871, fontFamily: 'MaterialIcons'),
  'person_4': IconData(0xf0872, fontFamily: 'MaterialIcons'),
  'person_add': IconData(0xe492, fontFamily: 'MaterialIcons'),
  'person_add_alt': IconData(0xe493, fontFamily: 'MaterialIcons'),
  'person_add_alt_1': IconData(0xe494, fontFamily: 'MaterialIcons'),
  'person_add_disabled': IconData(0xe495, fontFamily: 'MaterialIcons'),
  'person_off': IconData(0xe496, fontFamily: 'MaterialIcons'),
  'person_outline': IconData(0xe497, fontFamily: 'MaterialIcons'),
  'person_pin': IconData(0xe498, fontFamily: 'MaterialIcons'),
  'person_pin_circle': IconData(0xe499, fontFamily: 'MaterialIcons'),
  'person_remove': IconData(0xe49a, fontFamily: 'MaterialIcons'),
  'person_remove_alt_1': IconData(0xe49b, fontFamily: 'MaterialIcons'),
  'person_search': IconData(0xe49c, fontFamily: 'MaterialIcons'),
  'personal_injury': IconData(0xe49d, fontFamily: 'MaterialIcons'),
  'personal_video': IconData(0xe49e, fontFamily: 'MaterialIcons'),
  'pest_control': IconData(0xe49f, fontFamily: 'MaterialIcons'),
  'pest_control_rodent': IconData(0xe4a0, fontFamily: 'MaterialIcons'),
  'pets': IconData(0xe4a1, fontFamily: 'MaterialIcons'),
  'phishing': IconData(0xf0548, fontFamily: 'MaterialIcons'),
  'phone': IconData(0xe4a2, fontFamily: 'MaterialIcons'),
  'phone_android': IconData(0xe4a3, fontFamily: 'MaterialIcons'),
  'phone_bluetooth_speaker': IconData(0xe4a4, fontFamily: 'MaterialIcons'),
  'phone_callback': IconData(0xe4a5, fontFamily: 'MaterialIcons'),
  'phone_disabled': IconData(0xe4a6, fontFamily: 'MaterialIcons'),
  'phone_enabled': IconData(0xe4a7, fontFamily: 'MaterialIcons'),
  'phone_forwarded': IconData(0xe4a8, fontFamily: 'MaterialIcons'),
  'phone_in_talk': IconData(0xe4a9, fontFamily: 'MaterialIcons'),
  'phone_iphone': IconData(0xe4aa, fontFamily: 'MaterialIcons'),
  'phone_locked': IconData(0xe4ab, fontFamily: 'MaterialIcons'),
  'phone_missed': IconData(0xe4ac, fontFamily: 'MaterialIcons'),
  'phone_paused': IconData(0xe4ad, fontFamily: 'MaterialIcons'),
  'phonelink': IconData(0xe4ae, fontFamily: 'MaterialIcons'),
  'phonelink_erase': IconData(0xe4af, fontFamily: 'MaterialIcons'),
  'phonelink_lock': IconData(0xe4b0, fontFamily: 'MaterialIcons'),
  'phonelink_off': IconData(0xe4b1, fontFamily: 'MaterialIcons'),
  'phonelink_ring': IconData(0xe4b2, fontFamily: 'MaterialIcons'),
  'phonelink_setup': IconData(0xe4b3, fontFamily: 'MaterialIcons'),
  'photo': IconData(0xe4b4, fontFamily: 'MaterialIcons'),
  'photo_album': IconData(0xe4b5, fontFamily: 'MaterialIcons'),
  'photo_camera': IconData(0xe4b6, fontFamily: 'MaterialIcons'),
  'photo_camera_back': IconData(0xe4b7, fontFamily: 'MaterialIcons'),
  'photo_camera_front': IconData(0xe4b8, fontFamily: 'MaterialIcons'),
  'photo_filter': IconData(0xe4b9, fontFamily: 'MaterialIcons'),
  'photo_library': IconData(0xe4ba, fontFamily: 'MaterialIcons'),
  'photo_size_select_actual': IconData(0xe4bb, fontFamily: 'MaterialIcons'),
  'photo_size_select_large': IconData(0xe4bc, fontFamily: 'MaterialIcons'),
  'photo_size_select_small': IconData(0xe4bd, fontFamily: 'MaterialIcons'),
  'php': IconData(0xf0549, fontFamily: 'MaterialIcons'),
  'piano': IconData(0xe4be, fontFamily: 'MaterialIcons'),
  'piano_off': IconData(0xe4bf, fontFamily: 'MaterialIcons'),
  'picture_as_pdf': IconData(0xe4c0, fontFamily: 'MaterialIcons'),
  'picture_in_picture': IconData(0xe4c1, fontFamily: 'MaterialIcons'),
  'picture_in_picture_alt': IconData(0xe4c2, fontFamily: 'MaterialIcons'),
  'pie_chart': IconData(0xe4c3, fontFamily: 'MaterialIcons'),
  'pie_chart_outline': IconData(0xe4c5, fontFamily: 'MaterialIcons'),
  'pin': IconData(0xe4c6, fontFamily: 'MaterialIcons'),
  'pin_drop': IconData(0xe4c7, fontFamily: 'MaterialIcons'),
  'pin_end': IconData(0xf054b, fontFamily: 'MaterialIcons'),
  'pin_invoke': IconData(0xf054c, fontFamily: 'MaterialIcons'),
  'pinch': IconData(0xf054d, fontFamily: 'MaterialIcons'),
  'pivot_table_chart': IconData(0xe4c8, fontFamily: 'MaterialIcons'),
  'pix': IconData(0xf054e, fontFamily: 'MaterialIcons'),
  'place': IconData(0xe4c9, fontFamily: 'MaterialIcons'),
  'plagiarism': IconData(0xe4ca, fontFamily: 'MaterialIcons'),
  'play_arrow': IconData(0xe4cb, fontFamily: 'MaterialIcons'),
  'play_circle': IconData(0xe4cc, fontFamily: 'MaterialIcons'),
  'play_circle_fill': IconData(0xe4cd, fontFamily: 'MaterialIcons'),
  'play_circle_filled': IconData(0xe4cd, fontFamily: 'MaterialIcons'),
  'play_circle_outline': IconData(0xe4ce, fontFamily: 'MaterialIcons'),
  'play_disabled': IconData(0xe4cf, fontFamily: 'MaterialIcons'),
  'play_for_work': IconData(0xe4d0, fontFamily: 'MaterialIcons'),
  'play_lesson': IconData(0xe4d1, fontFamily: 'MaterialIcons'),
  'playlist_add':
      IconData(0xe4d2, fontFamily: 'MaterialIcons', matchTextDirection: true),
  'playlist_add_check': IconData(0xe4d3, fontFamily: 'MaterialIcons'),
  'playlist_add_check_circle': IconData(0xf054f, fontFamily: 'MaterialIcons'),
  'playlist_add_circle': IconData(0xf0550, fontFamily: 'MaterialIcons'),
  'playlist_play': IconData(0xe4d4, fontFamily: 'MaterialIcons'),
  'playlist_remove': IconData(0xf0551, fontFamily: 'MaterialIcons'),
  'plumbing': IconData(0xe4d5, fontFamily: 'MaterialIcons'),
  'plus_one': IconData(0xe4d6, fontFamily: 'MaterialIcons'),
  'podcasts': IconData(0xe4d7, fontFamily: 'MaterialIcons'),
  'point_of_sale': IconData(0xe4d8, fontFamily: 'MaterialIcons'),
  'policy': IconData(0xe4d9, fontFamily: 'MaterialIcons'),
  'poll': IconData(0xe4da, fontFamily: 'MaterialIcons'),
  'polyline': IconData(0xf0552, fontFamily: 'MaterialIcons'),
  'polymer': IconData(0xe4db, fontFamily: 'MaterialIcons'),
  'pool': IconData(0xe4dc, fontFamily: 'MaterialIcons'),
  'portable_wifi_off': IconData(0xe4dd, fontFamily: 'MaterialIcons'),
  'portrait': IconData(0xe4de, fontFamily: 'MaterialIcons'),
  'post_add': IconData(0xe4df, fontFamily: 'MaterialIcons'),
  'power': IconData(0xe4e0, fontFamily: 'MaterialIcons'),
  'power_input': IconData(0xe4e1, fontFamily: 'MaterialIcons'),
  'power_off': IconData(0xe4e2, fontFamily: 'MaterialIcons'),
  'power_settings_new': IconData(0xe4e3, fontFamily: 'MaterialIcons'),
  'precision_manufacturing': IconData(0xe4e4, fontFamily: 'MaterialIcons'),
  'pregnant_woman': IconData(0xe4e5, fontFamily: 'MaterialIcons'),
  'present_to_all': IconData(0xe4e6, fontFamily: 'MaterialIcons'),
  'preview': IconData(0xe4e7, fontFamily: 'MaterialIcons'),
  'price_change': IconData(0xe4e8, fontFamily: 'MaterialIcons'),
  'price_check': IconData(0xe4e9, fontFamily: 'MaterialIcons'),
  'print': IconData(0xe4ea, fontFamily: 'MaterialIcons'),
  'print_disabled': IconData(0xe4eb, fontFamily: 'MaterialIcons'),
  'priority_high': IconData(0xe4ec, fontFamily: 'MaterialIcons'),
  'privacy_tip': IconData(0xe4ed, fontFamily: 'MaterialIcons'),
  'private_connectivity': IconData(0xf0553, fontFamily: 'MaterialIcons'),
  'production_quantity_limits': IconData(0xe4ee, fontFamily: 'MaterialIcons'),
  'propane': IconData(0xf07b9, fontFamily: 'MaterialIcons'),
  'propane_tank': IconData(0xf07ba, fontFamily: 'MaterialIcons'),
  'psychology': IconData(0xe4ef, fontFamily: 'MaterialIcons'),
  'psychology_alt': IconData(0xf0873, fontFamily: 'MaterialIcons'),
  'public': IconData(0xe4f0, fontFamily: 'MaterialIcons'),
  'public_off': IconData(0xe4f1, fontFamily: 'MaterialIcons'),
  'publish': IconData(0xe4f2, fontFamily: 'MaterialIcons'),
  'published_with_changes': IconData(0xe4f3, fontFamily: 'MaterialIcons'),
  'punch_clock': IconData(0xf0554, fontFamily: 'MaterialIcons'),
  'push_pin': IconData(0xe4f4, fontFamily: 'MaterialIcons'),
  'qr_code': IconData(0xe4f5, fontFamily: 'MaterialIcons'),
  'qr_code_2': IconData(0xe4f6, fontFamily: 'MaterialIcons'),
  'qr_code_scanner': IconData(0xe4f7, fontFamily: 'MaterialIcons'),
  'query_builder': IconData(0xe4f8, fontFamily: 'MaterialIcons'),
  'query_stats': IconData(0xe4f9, fontFamily: 'MaterialIcons'),
  'question_answer': IconData(0xe4fa, fontFamily: 'MaterialIcons'),
  'question_mark': IconData(0xf0555, fontFamily: 'MaterialIcons'),
  'queue': IconData(0xe4fb, fontFamily: 'MaterialIcons'),
  'queue_music':
      IconData(0xe4fc, fontFamily: 'MaterialIcons', matchTextDirection: true),
  'queue_play_next': IconData(0xe4fd, fontFamily: 'MaterialIcons'),
  'quick_contacts_dialer': IconData(0xe18c, fontFamily: 'MaterialIcons'),
  'quick_contacts_mail': IconData(0xe18a, fontFamily: 'MaterialIcons'),
  'quickreply': IconData(0xe4fe, fontFamily: 'MaterialIcons'),
  'quiz': IconData(0xe4ff, fontFamily: 'MaterialIcons'),
  'quora': IconData(0xf0556, fontFamily: 'MaterialIcons'),
  'r_mobiledata': IconData(0xe500, fontFamily: 'MaterialIcons'),
  'radar': IconData(0xe501, fontFamily: 'MaterialIcons'),
  'radio': IconData(0xe502, fontFamily: 'MaterialIcons'),
  'radio_button_checked': IconData(0xe503, fontFamily: 'MaterialIcons'),
  'radio_button_off': IconData(0xe504, fontFamily: 'MaterialIcons'),
  'radio_button_on': IconData(0xe503, fontFamily: 'MaterialIcons'),
  'radio_button_unchecked': IconData(0xe504, fontFamily: 'MaterialIcons'),
  'railway_alert': IconData(0xe505, fontFamily: 'MaterialIcons'),
  'ramen_dining': IconData(0xe506, fontFamily: 'MaterialIcons'),
  'ramp_left': IconData(0xf0557, fontFamily: 'MaterialIcons'),
  'ramp_right': IconData(0xf0558, fontFamily: 'MaterialIcons'),
  'rate_review': IconData(0xe507, fontFamily: 'MaterialIcons'),
  'raw_off': IconData(0xe508, fontFamily: 'MaterialIcons'),
  'raw_on': IconData(0xe509, fontFamily: 'MaterialIcons'),
  'read_more': IconData(0xe50a, fontFamily: 'MaterialIcons'),
  'real_estate_agent': IconData(0xe50b, fontFamily: 'MaterialIcons'),
  'rebase_edit': IconData(0xf0874, fontFamily: 'MaterialIcons'),
  'receipt': IconData(0xe50c, fontFamily: 'MaterialIcons'),
  'receipt_long': IconData(0xe50d, fontFamily: 'MaterialIcons'),
  'recent_actors': IconData(0xe50e, fontFamily: 'MaterialIcons'),
  'recommend': IconData(0xe50f, fontFamily: 'MaterialIcons'),
  'record_voice_over': IconData(0xe510, fontFamily: 'MaterialIcons'),
  'rectangle': IconData(0xf0559, fontFamily: 'MaterialIcons'),
  'recycling': IconData(0xf055a, fontFamily: 'MaterialIcons'),
  'reddit': IconData(0xf055b, fontFamily: 'MaterialIcons'),
  'redeem': IconData(0xe511, fontFamily: 'MaterialIcons'),
  'redo':
      IconData(0xe512, fontFamily: 'MaterialIcons', matchTextDirection: true),
  'reduce_capacity': IconData(0xe513, fontFamily: 'MaterialIcons'),
  'refresh': IconData(0xe514, fontFamily: 'MaterialIcons'),
  'remember_me': IconData(0xe515, fontFamily: 'MaterialIcons'),
  'remove': IconData(0xe516, fontFamily: 'MaterialIcons'),
  'remove_circle': IconData(0xe517, fontFamily: 'MaterialIcons'),
  'remove_circle_outline': IconData(0xe518, fontFamily: 'MaterialIcons'),
  'remove_done': IconData(0xe519, fontFamily: 'MaterialIcons'),
  'remove_from_queue': IconData(0xe51a, fontFamily: 'MaterialIcons'),
  'remove_moderator': IconData(0xe51b, fontFamily: 'MaterialIcons'),
  'remove_red_eye': IconData(0xe51c, fontFamily: 'MaterialIcons'),
  'remove_road': IconData(0xf07bb, fontFamily: 'MaterialIcons'),
  'remove_shopping_cart': IconData(0xe51d, fontFamily: 'MaterialIcons'),
  'reorder': IconData(0xe51e, fontFamily: 'MaterialIcons'),
  'repartition': IconData(0xf0875, fontFamily: 'MaterialIcons'),
  'repeat': IconData(0xe51f, fontFamily: 'MaterialIcons'),
  'repeat_on': IconData(0xe520, fontFamily: 'MaterialIcons'),
  'repeat_one': IconData(0xe521, fontFamily: 'MaterialIcons'),
  'repeat_one_on': IconData(0xe522, fontFamily: 'MaterialIcons'),
  'replay': IconData(0xe523, fontFamily: 'MaterialIcons'),
  'replay_10': IconData(0xe524, fontFamily: 'MaterialIcons'),
  'replay_30': IconData(0xe525, fontFamily: 'MaterialIcons'),
  'replay_5': IconData(0xe526, fontFamily: 'MaterialIcons'),
  'replay_circle_filled': IconData(0xe527, fontFamily: 'MaterialIcons'),
  'reply':
      IconData(0xe528, fontFamily: 'MaterialIcons', matchTextDirection: true),
  'reply_all':
      IconData(0xe529, fontFamily: 'MaterialIcons', matchTextDirection: true),
  'report': IconData(0xe52a, fontFamily: 'MaterialIcons'),
  'report_gmailerrorred': IconData(0xe52b, fontFamily: 'MaterialIcons'),
  'report_off': IconData(0xe52c, fontFamily: 'MaterialIcons'),
  'report_problem': IconData(0xe52d, fontFamily: 'MaterialIcons'),
  'request_page': IconData(0xe52e, fontFamily: 'MaterialIcons'),
  'request_quote': IconData(0xe52f, fontFamily: 'MaterialIcons'),
  'reset_tv': IconData(0xe530, fontFamily: 'MaterialIcons'),
  'restart_alt': IconData(0xe531, fontFamily: 'MaterialIcons'),
  'restaurant': IconData(0xe532, fontFamily: 'MaterialIcons'),
  'restaurant_menu': IconData(0xe533, fontFamily: 'MaterialIcons'),
  'restore': IconData(0xe534, fontFamily: 'MaterialIcons'),
  'restore_from_trash': IconData(0xe535, fontFamily: 'MaterialIcons'),
  'restore_page': IconData(0xe536, fontFamily: 'MaterialIcons'),
  'reviews': IconData(0xe537, fontFamily: 'MaterialIcons'),
  'rice_bowl': IconData(0xe538, fontFamily: 'MaterialIcons'),
  'ring_volume': IconData(0xe539, fontFamily: 'MaterialIcons'),
  'rocket': IconData(0xf055c, fontFamily: 'MaterialIcons'),
  'rocket_launch': IconData(0xf055d, fontFamily: 'MaterialIcons'),
  'roller_shades': IconData(0xf07bc, fontFamily: 'MaterialIcons'),
  'roller_shades_closed': IconData(0xf07bd, fontFamily: 'MaterialIcons'),
  'roller_skating': IconData(0xf06c0, fontFamily: 'MaterialIcons'),
  'roofing': IconData(0xe53a, fontFamily: 'MaterialIcons'),
  'room': IconData(0xe53b, fontFamily: 'MaterialIcons'),
  'room_preferences': IconData(0xe53c, fontFamily: 'MaterialIcons'),
  'room_service': IconData(0xe53d, fontFamily: 'MaterialIcons'),
  'rotate_90_degrees_ccw': IconData(0xe53e, fontFamily: 'MaterialIcons'),
  'rotate_90_degrees_cw': IconData(0xf055e, fontFamily: 'MaterialIcons'),
  'rotate_left': IconData(0xe53f, fontFamily: 'MaterialIcons'),
  'rotate_right': IconData(0xe540, fontFamily: 'MaterialIcons'),
  'route': IconData(0xf0561, fontFamily: 'MaterialIcons'),
  'router': IconData(0xe542, fontFamily: 'MaterialIcons'),
  'rowing': IconData(0xe543, fontFamily: 'MaterialIcons'),
  'rss_feed': IconData(0xe544, fontFamily: 'MaterialIcons'),
  'rsvp': IconData(0xe545, fontFamily: 'MaterialIcons'),
  'rtt': IconData(0xe546, fontFamily: 'MaterialIcons'),
  'rule': IconData(0xe547, fontFamily: 'MaterialIcons'),
  'rule_folder': IconData(0xe548, fontFamily: 'MaterialIcons'),
  'run_circle': IconData(0xe549, fontFamily: 'MaterialIcons'),
  'running_with_errors': IconData(0xe54a, fontFamily: 'MaterialIcons'),
  'rv_hookup': IconData(0xe54b, fontFamily: 'MaterialIcons'),
  'safety_check': IconData(0xf07be, fontFamily: 'MaterialIcons'),
  'safety_divider': IconData(0xe54c, fontFamily: 'MaterialIcons'),
  'sailing': IconData(0xe54d, fontFamily: 'MaterialIcons'),
  'sanitizer': IconData(0xe54e, fontFamily: 'MaterialIcons'),
  'satellite': IconData(0xe54f, fontFamily: 'MaterialIcons'),
  'satellite_alt': IconData(0xf0562, fontFamily: 'MaterialIcons'),
  'save': IconData(0xe550, fontFamily: 'MaterialIcons'),
  'save_alt': IconData(0xe551, fontFamily: 'MaterialIcons'),
  'save_as': IconData(0xf0563, fontFamily: 'MaterialIcons'),
  'saved_search': IconData(0xe552, fontFamily: 'MaterialIcons'),
  'savings': IconData(0xe553, fontFamily: 'MaterialIcons'),
  'scale': IconData(0xf0564, fontFamily: 'MaterialIcons'),
  'scanner': IconData(0xe554, fontFamily: 'MaterialIcons'),
  'scatter_plot': IconData(0xe555, fontFamily: 'MaterialIcons'),
  'schedule': IconData(0xe556, fontFamily: 'MaterialIcons'),
  'schedule_send': IconData(0xe557, fontFamily: 'MaterialIcons'),
  'schema': IconData(0xe558, fontFamily: 'MaterialIcons'),
  'school': IconData(0xe559, fontFamily: 'MaterialIcons'),
  'science': IconData(0xe55a, fontFamily: 'MaterialIcons'),
  'score': IconData(0xe55b, fontFamily: 'MaterialIcons'),
  'scoreboard': IconData(0xf06c1, fontFamily: 'MaterialIcons'),
  'screen_lock_landscape': IconData(0xe55c, fontFamily: 'MaterialIcons'),
  'screen_lock_portrait': IconData(0xe55d, fontFamily: 'MaterialIcons'),
  'screen_lock_rotation': IconData(0xe55e, fontFamily: 'MaterialIcons'),
  'screen_rotation': IconData(0xe55f, fontFamily: 'MaterialIcons'),
  'screen_rotation_alt': IconData(0xf07bf, fontFamily: 'MaterialIcons'),
  'screen_search_desktop': IconData(0xe560, fontFamily: 'MaterialIcons'),
  'screen_share':
      IconData(0xe561, fontFamily: 'MaterialIcons', matchTextDirection: true),
  'screenshot': IconData(0xe562, fontFamily: 'MaterialIcons'),
  'screenshot_monitor': IconData(0xf07c0, fontFamily: 'MaterialIcons'),
  'scuba_diving': IconData(0xf06c2, fontFamily: 'MaterialIcons'),
  'sd': IconData(0xe563, fontFamily: 'MaterialIcons'),
  'sd_card': IconData(0xe564, fontFamily: 'MaterialIcons'),
  'sd_card_alert': IconData(0xe565, fontFamily: 'MaterialIcons'),
  'sd_storage': IconData(0xe566, fontFamily: 'MaterialIcons'),
  'search': IconData(0xe567, fontFamily: 'MaterialIcons'),
  'search_off': IconData(0xe568, fontFamily: 'MaterialIcons'),
  'security': IconData(0xe569, fontFamily: 'MaterialIcons'),
  'security_update': IconData(0xe56a, fontFamily: 'MaterialIcons'),
  'security_update_good': IconData(0xe56b, fontFamily: 'MaterialIcons'),
  'security_update_warning': IconData(0xe56c, fontFamily: 'MaterialIcons'),
  'segment': IconData(0xe56d, fontFamily: 'MaterialIcons'),
  'select_all': IconData(0xe56e, fontFamily: 'MaterialIcons'),
  'self_improvement': IconData(0xe56f, fontFamily: 'MaterialIcons'),
  'sell': IconData(0xe570, fontFamily: 'MaterialIcons'),
  'send':
      IconData(0xe571, fontFamily: 'MaterialIcons', matchTextDirection: true),
  'send_and_archive': IconData(0xe572, fontFamily: 'MaterialIcons'),
  'send_time_extension': IconData(0xf0565, fontFamily: 'MaterialIcons'),
  'send_to_mobile': IconData(0xe573, fontFamily: 'MaterialIcons'),
  'sensor_door': IconData(0xe574, fontFamily: 'MaterialIcons'),
  'sensor_occupied': IconData(0xf07c1, fontFamily: 'MaterialIcons'),
  'sensor_window': IconData(0xe575, fontFamily: 'MaterialIcons'),
  'sensors': IconData(0xe576, fontFamily: 'MaterialIcons'),
  'sensors_off': IconData(0xe577, fontFamily: 'MaterialIcons'),
  'sentiment_dissatisfied': IconData(0xe578, fontFamily: 'MaterialIcons'),
  'sentiment_neutral': IconData(0xe579, fontFamily: 'MaterialIcons'),
  'sentiment_satisfied': IconData(0xe57a, fontFamily: 'MaterialIcons'),
  'sentiment_satisfied_alt': IconData(0xe57b, fontFamily: 'MaterialIcons'),
  'sentiment_very_dissatisfied': IconData(0xe57c, fontFamily: 'MaterialIcons'),
  'sentiment_very_satisfied': IconData(0xe57d, fontFamily: 'MaterialIcons'),
  'set_meal': IconData(0xe57e, fontFamily: 'MaterialIcons'),
  'settings': IconData(0xe57f, fontFamily: 'MaterialIcons'),
  'settings_accessibility': IconData(0xe580, fontFamily: 'MaterialIcons'),
  'settings_applications': IconData(0xe581, fontFamily: 'MaterialIcons'),
  'settings_backup_restore': IconData(0xe582, fontFamily: 'MaterialIcons'),
  'settings_bluetooth': IconData(0xe583, fontFamily: 'MaterialIcons'),
  'settings_brightness': IconData(0xe584, fontFamily: 'MaterialIcons'),
  'settings_cell': IconData(0xe585, fontFamily: 'MaterialIcons'),
  'settings_display': IconData(0xe584, fontFamily: 'MaterialIcons'),
  'settings_ethernet': IconData(0xe586, fontFamily: 'MaterialIcons'),
  'settings_input_antenna': IconData(0xe587, fontFamily: 'MaterialIcons'),
  'settings_input_component': IconData(0xe588, fontFamily: 'MaterialIcons'),
  'settings_input_composite': IconData(0xe589, fontFamily: 'MaterialIcons'),
  'settings_input_hdmi': IconData(0xe58a, fontFamily: 'MaterialIcons'),
  'settings_input_svideo': IconData(0xe58b, fontFamily: 'MaterialIcons'),
  'settings_overscan': IconData(0xe58c, fontFamily: 'MaterialIcons'),
  'settings_phone': IconData(0xe58d, fontFamily: 'MaterialIcons'),
  'settings_power': IconData(0xe58e, fontFamily: 'MaterialIcons'),
  'settings_remote': IconData(0xe58f, fontFamily: 'MaterialIcons'),
  'settings_suggest': IconData(0xe590, fontFamily: 'MaterialIcons'),
  'settings_system_daydream': IconData(0xe591, fontFamily: 'MaterialIcons'),
  'settings_voice': IconData(0xe592, fontFamily: 'MaterialIcons'),
  'severe_cold': IconData(0xf07c2, fontFamily: 'MaterialIcons'),
  'shape_line': IconData(0xf0876, fontFamily: 'MaterialIcons'),
  'share': IconData(0xe593, fontFamily: 'MaterialIcons'),
  'share_arrival_time': IconData(0xe594, fontFamily: 'MaterialIcons'),
  'share_location': IconData(0xe595, fontFamily: 'MaterialIcons'),
  'shelves': IconData(0xf0877, fontFamily: 'MaterialIcons'),
  'shield': IconData(0xe596, fontFamily: 'MaterialIcons'),
  'shield_moon': IconData(0xf0566, fontFamily: 'MaterialIcons'),
  'shop': IconData(0xe597, fontFamily: 'MaterialIcons'),
  'shop_2': IconData(0xe598, fontFamily: 'MaterialIcons'),
  'shop_two': IconData(0xe599, fontFamily: 'MaterialIcons'),
  'shopify': IconData(0xf0567, fontFamily: 'MaterialIcons'),
  'shopping_bag': IconData(0xe59a, fontFamily: 'MaterialIcons'),
  'shopping_basket': IconData(0xe59b, fontFamily: 'MaterialIcons'),
  'shopping_cart': IconData(0xe59c, fontFamily: 'MaterialIcons'),
  'shopping_cart_checkout': IconData(0xf0568, fontFamily: 'MaterialIcons'),
  'short_text':
      IconData(0xe59d, fontFamily: 'MaterialIcons', matchTextDirection: true),
  'shortcut': IconData(0xe59e, fontFamily: 'MaterialIcons'),
  'show_chart':
      IconData(0xe59f, fontFamily: 'MaterialIcons', matchTextDirection: true),
  'shower': IconData(0xe5a0, fontFamily: 'MaterialIcons'),
  'shuffle': IconData(0xe5a1, fontFamily: 'MaterialIcons'),
  'shuffle_on': IconData(0xe5a2, fontFamily: 'MaterialIcons'),
  'shutter_speed': IconData(0xe5a3, fontFamily: 'MaterialIcons'),
  'sick': IconData(0xe5a4, fontFamily: 'MaterialIcons'),
  'sign_language': IconData(0xf07c3, fontFamily: 'MaterialIcons'),
  'signal_cellular_0_bar': IconData(0xe5a5, fontFamily: 'MaterialIcons'),
  'signal_cellular_4_bar': IconData(0xe5a6, fontFamily: 'MaterialIcons'),
  'signal_cellular_alt': IconData(0xe5a7, fontFamily: 'MaterialIcons'),
  'signal_cellular_alt_1_bar': IconData(0xf07c4, fontFamily: 'MaterialIcons'),
  'signal_cellular_alt_2_bar': IconData(0xf07c5, fontFamily: 'MaterialIcons'),
  'signal_cellular_connected_no_internet_0_bar':
      IconData(0xe5a8, fontFamily: 'MaterialIcons'),
  'signal_cellular_connected_no_internet_4_bar':
      IconData(0xe5a9, fontFamily: 'MaterialIcons'),
  'signal_cellular_no_sim': IconData(0xe5aa, fontFamily: 'MaterialIcons'),
  'signal_cellular_nodata': IconData(0xe5ab, fontFamily: 'MaterialIcons'),
  'signal_cellular_null': IconData(0xe5ac, fontFamily: 'MaterialIcons'),
  'signal_cellular_off': IconData(0xe5ad, fontFamily: 'MaterialIcons'),
  'signal_wifi_0_bar': IconData(0xe5ae, fontFamily: 'MaterialIcons'),
  'signal_wifi_4_bar': IconData(0xe5af, fontFamily: 'MaterialIcons'),
  'signal_wifi_4_bar_lock': IconData(0xe5b0, fontFamily: 'MaterialIcons'),
  'signal_wifi_bad': IconData(0xe5b1, fontFamily: 'MaterialIcons'),
  'signal_wifi_connected_no_internet_4':
      IconData(0xe5b2, fontFamily: 'MaterialIcons'),
  'signal_wifi_off': IconData(0xe5b3, fontFamily: 'MaterialIcons'),
  'signal_wifi_statusbar_4_bar': IconData(0xe5b4, fontFamily: 'MaterialIcons'),
  'signal_wifi_statusbar_connected_no_internet_4':
      IconData(0xe5b5, fontFamily: 'MaterialIcons'),
  'signal_wifi_statusbar_null': IconData(0xe5b6, fontFamily: 'MaterialIcons'),
  'signpost': IconData(0xf0569, fontFamily: 'MaterialIcons'),
  'sim_card': IconData(0xe5b7, fontFamily: 'MaterialIcons'),
  'sim_card_alert': IconData(0xe5b8, fontFamily: 'MaterialIcons'),
  'sim_card_download': IconData(0xe5b9, fontFamily: 'MaterialIcons'),
  'single_bed': IconData(0xe5ba, fontFamily: 'MaterialIcons'),
  'sip': IconData(0xe5bb, fontFamily: 'MaterialIcons'),
  'skateboarding': IconData(0xe5bc, fontFamily: 'MaterialIcons'),
  'skip_next': IconData(0xe5bd, fontFamily: 'MaterialIcons'),
  'skip_previous': IconData(0xe5be, fontFamily: 'MaterialIcons'),
  'sledding': IconData(0xe5bf, fontFamily: 'MaterialIcons'),
  'slideshow': IconData(0xe5c0, fontFamily: 'MaterialIcons'),
  'slow_motion_video': IconData(0xe5c1, fontFamily: 'MaterialIcons'),
  'smart_button': IconData(0xe5c2, fontFamily: 'MaterialIcons'),
  'smart_display': IconData(0xe5c3, fontFamily: 'MaterialIcons'),
  'smart_screen': IconData(0xe5c4, fontFamily: 'MaterialIcons'),
  'smart_toy': IconData(0xe5c5, fontFamily: 'MaterialIcons'),
  'smartphone': IconData(0xe5c6, fontFamily: 'MaterialIcons'),
  'smoke_free': IconData(0xe5c7, fontFamily: 'MaterialIcons'),
  'smoking_rooms': IconData(0xe5c8, fontFamily: 'MaterialIcons'),
  'sms': IconData(0xe5c9, fontFamily: 'MaterialIcons'),
  'sms_failed': IconData(0xe5ca, fontFamily: 'MaterialIcons'),
  'snapchat': IconData(0xf056a, fontFamily: 'MaterialIcons'),
  'snippet_folder': IconData(0xe5cb, fontFamily: 'MaterialIcons'),
  'snooze': IconData(0xe5cc, fontFamily: 'MaterialIcons'),
  'snowboarding': IconData(0xe5cd, fontFamily: 'MaterialIcons'),
  'snowing': IconData(0xf056b, fontFamily: 'MaterialIcons'),
  'snowmobile': IconData(0xe5ce, fontFamily: 'MaterialIcons'),
  'snowshoeing': IconData(0xe5cf, fontFamily: 'MaterialIcons'),
  'soap': IconData(0xe5d0, fontFamily: 'MaterialIcons'),
  'social_distance': IconData(0xe5d1, fontFamily: 'MaterialIcons'),
  'solar_power': IconData(0xf07c6, fontFamily: 'MaterialIcons'),
  'sort':
      IconData(0xe5d2, fontFamily: 'MaterialIcons', matchTextDirection: true),
  'sort_by_alpha': IconData(0xe5d3, fontFamily: 'MaterialIcons'),
  'sos': IconData(0xf07c7, fontFamily: 'MaterialIcons'),
  'soup_kitchen': IconData(0xf056c, fontFamily: 'MaterialIcons'),
  'source': IconData(0xe5d4, fontFamily: 'MaterialIcons'),
  'south': IconData(0xe5d5, fontFamily: 'MaterialIcons'),
  'south_america': IconData(0xf056d, fontFamily: 'MaterialIcons'),
  'south_east': IconData(0xe5d6, fontFamily: 'MaterialIcons'),
  'south_west': IconData(0xe5d7, fontFamily: 'MaterialIcons'),
  'spa': IconData(0xe5d8, fontFamily: 'MaterialIcons'),
  'space_bar': IconData(0xe5d9, fontFamily: 'MaterialIcons'),
  'space_dashboard': IconData(0xe5da, fontFamily: 'MaterialIcons'),
  'spatial_audio': IconData(0xf07c8, fontFamily: 'MaterialIcons'),
  'spatial_audio_off': IconData(0xf07c9, fontFamily: 'MaterialIcons'),
  'spatial_tracking': IconData(0xf07ca, fontFamily: 'MaterialIcons'),
  'speaker': IconData(0xe5db, fontFamily: 'MaterialIcons'),
  'speaker_group': IconData(0xe5dc, fontFamily: 'MaterialIcons'),
  'speaker_notes': IconData(0xe5dd, fontFamily: 'MaterialIcons'),
  'speaker_notes_off': IconData(0xe5de, fontFamily: 'MaterialIcons'),
  'speaker_phone': IconData(0xe5df, fontFamily: 'MaterialIcons'),
  'speed': IconData(0xe5e0, fontFamily: 'MaterialIcons'),
  'spellcheck': IconData(0xe5e1, fontFamily: 'MaterialIcons'),
  'splitscreen': IconData(0xe5e2, fontFamily: 'MaterialIcons'),
  'spoke': IconData(0xf056e, fontFamily: 'MaterialIcons'),
  'sports': IconData(0xe5e3, fontFamily: 'MaterialIcons'),
  'sports_bar': IconData(0xe5e4, fontFamily: 'MaterialIcons'),
  'sports_baseball': IconData(0xe5e5, fontFamily: 'MaterialIcons'),
  'sports_basketball': IconData(0xe5e6, fontFamily: 'MaterialIcons'),
  'sports_cricket': IconData(0xe5e7, fontFamily: 'MaterialIcons'),
  'sports_esports': IconData(0xe5e8, fontFamily: 'MaterialIcons'),
  'sports_football': IconData(0xe5e9, fontFamily: 'MaterialIcons'),
  'sports_golf': IconData(0xe5ea, fontFamily: 'MaterialIcons'),
  'sports_gymnastics': IconData(0xf06c3, fontFamily: 'MaterialIcons'),
  'sports_handball': IconData(0xe5eb, fontFamily: 'MaterialIcons'),
  'sports_hockey': IconData(0xe5ec, fontFamily: 'MaterialIcons'),
  'sports_kabaddi': IconData(0xe5ed, fontFamily: 'MaterialIcons'),
  'sports_martial_arts': IconData(0xf056f, fontFamily: 'MaterialIcons'),
  'sports_mma': IconData(0xe5ee, fontFamily: 'MaterialIcons'),
  'sports_motorsports': IconData(0xe5ef, fontFamily: 'MaterialIcons'),
  'sports_rugby': IconData(0xe5f0, fontFamily: 'MaterialIcons'),
  'sports_score': IconData(0xe5f1, fontFamily: 'MaterialIcons'),
  'sports_soccer': IconData(0xe5f2, fontFamily: 'MaterialIcons'),
  'sports_tennis': IconData(0xe5f3, fontFamily: 'MaterialIcons'),
  'sports_volleyball': IconData(0xe5f4, fontFamily: 'MaterialIcons'),
  'square': IconData(0xf0570, fontFamily: 'MaterialIcons'),
  'square_foot': IconData(0xe5f5, fontFamily: 'MaterialIcons'),
  'ssid_chart': IconData(0xf0571, fontFamily: 'MaterialIcons'),
  'stacked_bar_chart': IconData(0xe5f6, fontFamily: 'MaterialIcons'),
  'stacked_line_chart': IconData(0xe5f7, fontFamily: 'MaterialIcons'),
  'stadium': IconData(0xf0572, fontFamily: 'MaterialIcons'),
  'stairs': IconData(0xe5f8, fontFamily: 'MaterialIcons'),
  'star': IconData(0xe5f9, fontFamily: 'MaterialIcons'),
  'star_border': IconData(0xe5fa, fontFamily: 'MaterialIcons'),
  'star_border_purple500': IconData(0xe5fb, fontFamily: 'MaterialIcons'),
  'star_half':
      IconData(0xe5fc, fontFamily: 'MaterialIcons', matchTextDirection: true),
  'star_outline': IconData(0xe5fd, fontFamily: 'MaterialIcons'),
  'star_purple500': IconData(0xe5fe, fontFamily: 'MaterialIcons'),
  'star_rate': IconData(0xe5ff, fontFamily: 'MaterialIcons'),
  'stars': IconData(0xe600, fontFamily: 'MaterialIcons'),
  'start': IconData(0xf0573, fontFamily: 'MaterialIcons'),
  'stay_current_landscape': IconData(0xe601, fontFamily: 'MaterialIcons'),
  'stay_current_portrait': IconData(0xe602, fontFamily: 'MaterialIcons'),
  'stay_primary_landscape': IconData(0xe603, fontFamily: 'MaterialIcons'),
  'stay_primary_portrait': IconData(0xe604, fontFamily: 'MaterialIcons'),
  'sticky_note_2': IconData(0xe605, fontFamily: 'MaterialIcons'),
  'stop': IconData(0xe606, fontFamily: 'MaterialIcons'),
  'stop_circle': IconData(0xe607, fontFamily: 'MaterialIcons'),
  'stop_screen_share': IconData(0xe608, fontFamily: 'MaterialIcons'),
  'storage': IconData(0xe609, fontFamily: 'MaterialIcons'),
  'store': IconData(0xe60a, fontFamily: 'MaterialIcons'),
  'store_mall_directory': IconData(0xe60b, fontFamily: 'MaterialIcons'),
  'storefront': IconData(0xe60c, fontFamily: 'MaterialIcons'),
  'storm': IconData(0xe60d, fontFamily: 'MaterialIcons'),
  'straight': IconData(0xf0574, fontFamily: 'MaterialIcons'),
  'straighten': IconData(0xe60e, fontFamily: 'MaterialIcons'),
  'stream': IconData(0xe60f, fontFamily: 'MaterialIcons'),
  'streetview': IconData(0xe610, fontFamily: 'MaterialIcons'),
  'strikethrough_s': IconData(0xe611, fontFamily: 'MaterialIcons'),
  'stroller': IconData(0xe612, fontFamily: 'MaterialIcons'),
  'style': IconData(0xe613, fontFamily: 'MaterialIcons'),
  'subdirectory_arrow_left': IconData(0xe614, fontFamily: 'MaterialIcons'),
  'subdirectory_arrow_right': IconData(0xe615, fontFamily: 'MaterialIcons'),
  'subject':
      IconData(0xe616, fontFamily: 'MaterialIcons', matchTextDirection: true),
  'subscript': IconData(0xe617, fontFamily: 'MaterialIcons'),
  'subscriptions': IconData(0xe618, fontFamily: 'MaterialIcons'),
  'subtitles': IconData(0xe619, fontFamily: 'MaterialIcons'),
  'subtitles_off': IconData(0xe61a, fontFamily: 'MaterialIcons'),
  'subway': IconData(0xe61b, fontFamily: 'MaterialIcons'),
  'summarize': IconData(0xe61c, fontFamily: 'MaterialIcons'),
  'sunny': IconData(0xf0575, fontFamily: 'MaterialIcons'),
  'sunny_snowing': IconData(0xf0576, fontFamily: 'MaterialIcons'),
  'superscript': IconData(0xe61d, fontFamily: 'MaterialIcons'),
  'supervised_user_circle': IconData(0xe61e, fontFamily: 'MaterialIcons'),
  'supervisor_account': IconData(0xe61f, fontFamily: 'MaterialIcons'),
  'support': IconData(0xe620, fontFamily: 'MaterialIcons'),
  'support_agent': IconData(0xe621, fontFamily: 'MaterialIcons'),
  'surfing': IconData(0xe622, fontFamily: 'MaterialIcons'),
  'swap_calls': IconData(0xe624, fontFamily: 'MaterialIcons'),
  'swap_horiz': IconData(0xe625, fontFamily: 'MaterialIcons'),
  'swap_horizontal_circle': IconData(0xe626, fontFamily: 'MaterialIcons'),
  'swap_vert': IconData(0xe627, fontFamily: 'MaterialIcons'),
  'swap_vert_circle': IconData(0xe628, fontFamily: 'MaterialIcons'),
  'swap_vertical_circle': IconData(0xe628, fontFamily: 'MaterialIcons'),
  'swipe': IconData(0xe629, fontFamily: 'MaterialIcons'),
  'swipe_down': IconData(0xf0578, fontFamily: 'MaterialIcons'),
  'swipe_down_alt': IconData(0xf0577, fontFamily: 'MaterialIcons'),
  'swipe_left': IconData(0xf057a, fontFamily: 'MaterialIcons'),
  'swipe_left_alt': IconData(0xf0579, fontFamily: 'MaterialIcons'),
  'swipe_right': IconData(0xf057c, fontFamily: 'MaterialIcons'),
  'swipe_right_alt': IconData(0xf057b, fontFamily: 'MaterialIcons'),
  'swipe_up': IconData(0xf057e, fontFamily: 'MaterialIcons'),
  'swipe_up_alt': IconData(0xf057d, fontFamily: 'MaterialIcons'),
  'swipe_vertical': IconData(0xf057f, fontFamily: 'MaterialIcons'),
  'switch_access_shortcut': IconData(0xf0581, fontFamily: 'MaterialIcons'),
  'switch_access_shortcut_add': IconData(0xf0580, fontFamily: 'MaterialIcons'),
  'switch_account': IconData(0xe62a, fontFamily: 'MaterialIcons'),
  'switch_camera': IconData(0xe62b, fontFamily: 'MaterialIcons'),
  'switch_left': IconData(0xe62c, fontFamily: 'MaterialIcons'),
  'switch_right': IconData(0xe62d, fontFamily: 'MaterialIcons'),
  'switch_video': IconData(0xe62e, fontFamily: 'MaterialIcons'),
  'synagogue': IconData(0xf0582, fontFamily: 'MaterialIcons'),
  'sync': IconData(0xe62f, fontFamily: 'MaterialIcons'),
  'sync_alt': IconData(0xe630, fontFamily: 'MaterialIcons'),
  'sync_disabled': IconData(0xe631, fontFamily: 'MaterialIcons'),
  'sync_lock': IconData(0xf0583, fontFamily: 'MaterialIcons'),
  'sync_problem': IconData(0xe632, fontFamily: 'MaterialIcons'),
  'system_security_update': IconData(0xe633, fontFamily: 'MaterialIcons'),
  'system_security_update_good': IconData(0xe634, fontFamily: 'MaterialIcons'),
  'system_security_update_warning':
      IconData(0xe635, fontFamily: 'MaterialIcons'),
  'system_update': IconData(0xe636, fontFamily: 'MaterialIcons'),
  'system_update_alt': IconData(0xe637, fontFamily: 'MaterialIcons'),
  'system_update_tv': IconData(0xe637, fontFamily: 'MaterialIcons'),
  'tab': IconData(0xe638, fontFamily: 'MaterialIcons'),
  'tab_unselected': IconData(0xe639, fontFamily: 'MaterialIcons'),
  'table_bar': IconData(0xf0584, fontFamily: 'MaterialIcons'),
  'table_chart': IconData(0xe63a, fontFamily: 'MaterialIcons'),
  'table_restaurant': IconData(0xf0585, fontFamily: 'MaterialIcons'),
  'table_rows': IconData(0xe63b, fontFamily: 'MaterialIcons'),
  'table_view': IconData(0xe63c, fontFamily: 'MaterialIcons'),
  'tablet': IconData(0xe63d, fontFamily: 'MaterialIcons'),
  'tablet_android': IconData(0xe63e, fontFamily: 'MaterialIcons'),
  'tablet_mac': IconData(0xe63f, fontFamily: 'MaterialIcons'),
  'tag': IconData(0xe640, fontFamily: 'MaterialIcons'),
  'tag_faces': IconData(0xe641, fontFamily: 'MaterialIcons'),
  'takeout_dining': IconData(0xe642, fontFamily: 'MaterialIcons'),
  'tap_and_play': IconData(0xe643, fontFamily: 'MaterialIcons'),
  'tapas': IconData(0xe644, fontFamily: 'MaterialIcons'),
  'task': IconData(0xe645, fontFamily: 'MaterialIcons'),
  'task_alt': IconData(0xe646, fontFamily: 'MaterialIcons'),
  'taxi_alert': IconData(0xe647, fontFamily: 'MaterialIcons'),
  'telegram': IconData(0xf0586, fontFamily: 'MaterialIcons'),
  'temple_buddhist': IconData(0xf0587, fontFamily: 'MaterialIcons'),
  'temple_hindu': IconData(0xf0588, fontFamily: 'MaterialIcons'),
  'terminal': IconData(0xf0589, fontFamily: 'MaterialIcons'),
  'terrain': IconData(0xe648, fontFamily: 'MaterialIcons'),
  'text_decrease': IconData(0xf058a, fontFamily: 'MaterialIcons'),
  'text_fields': IconData(0xe649, fontFamily: 'MaterialIcons'),
  'text_format': IconData(0xe64a, fontFamily: 'MaterialIcons'),
  'text_increase': IconData(0xf058b, fontFamily: 'MaterialIcons'),
  'text_rotate_up': IconData(0xe64b, fontFamily: 'MaterialIcons'),
  'text_rotate_vertical': IconData(0xe64c, fontFamily: 'MaterialIcons'),
  'text_rotation_angledown': IconData(0xe64d, fontFamily: 'MaterialIcons'),
  'text_rotation_angleup': IconData(0xe64e, fontFamily: 'MaterialIcons'),
  'text_rotation_down': IconData(0xe64f, fontFamily: 'MaterialIcons'),
  'text_rotation_none': IconData(0xe650, fontFamily: 'MaterialIcons'),
  'text_snippet': IconData(0xe651, fontFamily: 'MaterialIcons'),
  'textsms': IconData(0xe652, fontFamily: 'MaterialIcons'),
  'texture': IconData(0xe653, fontFamily: 'MaterialIcons'),
  'theater_comedy': IconData(0xe654, fontFamily: 'MaterialIcons'),
  'theaters': IconData(0xe655, fontFamily: 'MaterialIcons'),
  'thermostat': IconData(0xe656, fontFamily: 'MaterialIcons'),
  'thermostat_auto': IconData(0xe657, fontFamily: 'MaterialIcons'),
  'thumb_down': IconData(0xe658, fontFamily: 'MaterialIcons'),
  'thumb_down_alt': IconData(0xe659, fontFamily: 'MaterialIcons'),
  'thumb_down_off_alt': IconData(0xe65a, fontFamily: 'MaterialIcons'),
  'thumb_up': IconData(0xe65b, fontFamily: 'MaterialIcons'),
  'thumb_up_alt': IconData(0xe65c, fontFamily: 'MaterialIcons'),
  'thumb_up_off_alt': IconData(0xe65d, fontFamily: 'MaterialIcons'),
  'thumbs_up_down': IconData(0xe65e, fontFamily: 'MaterialIcons'),
  'thunderstorm': IconData(0xf07cb, fontFamily: 'MaterialIcons'),
  'tiktok': IconData(0xf058c, fontFamily: 'MaterialIcons'),
  'time_to_leave': IconData(0xe65f, fontFamily: 'MaterialIcons'),
  'timelapse': IconData(0xe660, fontFamily: 'MaterialIcons'),
  'timeline': IconData(0xe661, fontFamily: 'MaterialIcons'),
  'timer': IconData(0xe662, fontFamily: 'MaterialIcons'),
  'timer_10': IconData(0xe663, fontFamily: 'MaterialIcons'),
  'timer_10_select': IconData(0xe664, fontFamily: 'MaterialIcons'),
  'timer_3': IconData(0xe665, fontFamily: 'MaterialIcons'),
  'timer_3_select': IconData(0xe666, fontFamily: 'MaterialIcons'),
  'timer_off': IconData(0xe667, fontFamily: 'MaterialIcons'),
  'tips_and_updates': IconData(0xf058d, fontFamily: 'MaterialIcons'),
  'tire_repair': IconData(0xf06c4, fontFamily: 'MaterialIcons'),
  'title': IconData(0xe668, fontFamily: 'MaterialIcons'),
  'toc':
      IconData(0xe669, fontFamily: 'MaterialIcons', matchTextDirection: true),
  'today': IconData(0xe66a, fontFamily: 'MaterialIcons'),
  'toggle_off': IconData(0xe66b, fontFamily: 'MaterialIcons'),
  'toggle_on': IconData(0xe66c, fontFamily: 'MaterialIcons'),
  'token': IconData(0xf058e, fontFamily: 'MaterialIcons'),
  'toll': IconData(0xe66d, fontFamily: 'MaterialIcons'),
  'tonality': IconData(0xe66e, fontFamily: 'MaterialIcons'),
  'topic': IconData(0xe66f, fontFamily: 'MaterialIcons'),
  'tornado': IconData(0xf07cc, fontFamily: 'MaterialIcons'),
  'touch_app': IconData(0xe670, fontFamily: 'MaterialIcons'),
  'tour': IconData(0xe671, fontFamily: 'MaterialIcons'),
  'toys': IconData(0xe672, fontFamily: 'MaterialIcons'),
  'track_changes': IconData(0xe673, fontFamily: 'MaterialIcons'),
  'traffic': IconData(0xe674, fontFamily: 'MaterialIcons'),
  'train': IconData(0xe675, fontFamily: 'MaterialIcons'),
  'tram': IconData(0xe676, fontFamily: 'MaterialIcons'),
  'transcribe': IconData(0xf07cd, fontFamily: 'MaterialIcons'),
  'transfer_within_a_station': IconData(0xe677, fontFamily: 'MaterialIcons'),
  'transform': IconData(0xe678, fontFamily: 'MaterialIcons'),
  'transgender': IconData(0xe679, fontFamily: 'MaterialIcons'),
  'transit_enterexit': IconData(0xe67a, fontFamily: 'MaterialIcons'),
  'translate': IconData(0xe67b, fontFamily: 'MaterialIcons'),
  'travel_explore': IconData(0xe67c, fontFamily: 'MaterialIcons'),
  'trending_down':
      IconData(0xe67d, fontFamily: 'MaterialIcons', matchTextDirection: true),
  'trending_flat':
      IconData(0xe67e, fontFamily: 'MaterialIcons', matchTextDirection: true),
  'trending_neutral': IconData(0xe67e, fontFamily: 'MaterialIcons'),
  'trending_up':
      IconData(0xe67f, fontFamily: 'MaterialIcons', matchTextDirection: true),
  'trip_origin': IconData(0xe680, fontFamily: 'MaterialIcons'),
  'trolley': IconData(0xf0878, fontFamily: 'MaterialIcons'),
  'troubleshoot': IconData(0xf07ce, fontFamily: 'MaterialIcons'),
  'try': IconData(0xe681, fontFamily: 'MaterialIcons'),
  'tsunami': IconData(0xf07cf, fontFamily: 'MaterialIcons'),
  'tty': IconData(0xe682, fontFamily: 'MaterialIcons'),
  'tune': IconData(0xe683, fontFamily: 'MaterialIcons'),
  'tungsten': IconData(0xe684, fontFamily: 'MaterialIcons'),
  'turn_left': IconData(0xf058f, fontFamily: 'MaterialIcons'),
  'turn_right': IconData(0xf0590, fontFamily: 'MaterialIcons'),
  'turn_slight_left': IconData(0xf0593, fontFamily: 'MaterialIcons'),
  'turn_slight_right': IconData(0xf0594, fontFamily: 'MaterialIcons'),
  'turned_in': IconData(0xe685, fontFamily: 'MaterialIcons'),
  'turned_in_not': IconData(0xe686, fontFamily: 'MaterialIcons'),
  'tv': IconData(0xe687, fontFamily: 'MaterialIcons'),
  'tv_off': IconData(0xe688, fontFamily: 'MaterialIcons'),
  'two_wheeler': IconData(0xe689, fontFamily: 'MaterialIcons'),
  'type_specimen': IconData(0xf07d0, fontFamily: 'MaterialIcons'),
  'u_turn_left': IconData(0xf0595, fontFamily: 'MaterialIcons'),
  'u_turn_right': IconData(0xf0596, fontFamily: 'MaterialIcons'),
  'umbrella': IconData(0xe68a, fontFamily: 'MaterialIcons'),
  'unarchive': IconData(0xe68b, fontFamily: 'MaterialIcons'),
  'undo':
      IconData(0xe68c, fontFamily: 'MaterialIcons', matchTextDirection: true),
  'unfold_less': IconData(0xe68d, fontFamily: 'MaterialIcons'),
  'unfold_less_double': IconData(0xf0879, fontFamily: 'MaterialIcons'),
  'unfold_more': IconData(0xe68e, fontFamily: 'MaterialIcons'),
  'unfold_more_double': IconData(0xf087a, fontFamily: 'MaterialIcons'),
  'unpublished': IconData(0xe68f, fontFamily: 'MaterialIcons'),
  'unsubscribe': IconData(0xe690, fontFamily: 'MaterialIcons'),
  'upcoming': IconData(0xe691, fontFamily: 'MaterialIcons'),
  'update': IconData(0xe692, fontFamily: 'MaterialIcons'),
  'update_disabled': IconData(0xe693, fontFamily: 'MaterialIcons'),
  'upgrade': IconData(0xe694, fontFamily: 'MaterialIcons'),
  'upload': IconData(0xe695, fontFamily: 'MaterialIcons'),
  'upload_file': IconData(0xe696, fontFamily: 'MaterialIcons'),
  'usb': IconData(0xe697, fontFamily: 'MaterialIcons'),
  'usb_off': IconData(0xe698, fontFamily: 'MaterialIcons'),
  'vaccines': IconData(0xf0597, fontFamily: 'MaterialIcons'),
  'vape_free': IconData(0xf06c5, fontFamily: 'MaterialIcons'),
  'vaping_rooms': IconData(0xf06c6, fontFamily: 'MaterialIcons'),
  'verified': IconData(0xe699, fontFamily: 'MaterialIcons'),
  'verified_user': IconData(0xe69a, fontFamily: 'MaterialIcons'),
  'vertical_align_bottom': IconData(0xe69b, fontFamily: 'MaterialIcons'),
  'vertical_align_center': IconData(0xe69c, fontFamily: 'MaterialIcons'),
  'vertical_align_top': IconData(0xe69d, fontFamily: 'MaterialIcons'),
  'vertical_distribute': IconData(0xe69e, fontFamily: 'MaterialIcons'),
  'vertical_shades': IconData(0xf07d1, fontFamily: 'MaterialIcons'),
  'vertical_shades_closed': IconData(0xf07d2, fontFamily: 'MaterialIcons'),
  'vertical_split': IconData(0xe69f, fontFamily: 'MaterialIcons'),
  'vibration': IconData(0xe6a0, fontFamily: 'MaterialIcons'),
  'video_call': IconData(0xe6a1, fontFamily: 'MaterialIcons'),
  'video_camera_back': IconData(0xe6a2, fontFamily: 'MaterialIcons'),
  'video_camera_front': IconData(0xe6a3, fontFamily: 'MaterialIcons'),
  'video_chat': IconData(0xf087b, fontFamily: 'MaterialIcons'),
  'video_collection': IconData(0xe6a5, fontFamily: 'MaterialIcons'),
  'video_file': IconData(0xf0598, fontFamily: 'MaterialIcons'),
  'video_label': IconData(0xe6a4, fontFamily: 'MaterialIcons'),
  'video_library': IconData(0xe6a5, fontFamily: 'MaterialIcons'),
  'video_settings': IconData(0xe6a6, fontFamily: 'MaterialIcons'),
  'video_stable': IconData(0xe6a7, fontFamily: 'MaterialIcons'),
  'videocam': IconData(0xe6a8, fontFamily: 'MaterialIcons'),
  'videocam_off': IconData(0xe6a9, fontFamily: 'MaterialIcons'),
  'videogame_asset': IconData(0xe6aa, fontFamily: 'MaterialIcons'),
  'videogame_asset_off': IconData(0xe6ab, fontFamily: 'MaterialIcons'),
  'view_agenda': IconData(0xe6ac, fontFamily: 'MaterialIcons'),
  'view_array': IconData(0xe6ad, fontFamily: 'MaterialIcons'),
  'view_carousel': IconData(0xe6ae, fontFamily: 'MaterialIcons'),
  'view_column': IconData(0xe6af, fontFamily: 'MaterialIcons'),
  'view_comfortable': IconData(0xe6b0, fontFamily: 'MaterialIcons'),
  'view_comfy': IconData(0xe6b0, fontFamily: 'MaterialIcons'),
  'view_comfy_alt': IconData(0xf0599, fontFamily: 'MaterialIcons'),
  'view_compact': IconData(0xe6b1, fontFamily: 'MaterialIcons'),
  'view_compact_alt': IconData(0xf059a, fontFamily: 'MaterialIcons'),
  'view_cozy': IconData(0xf059b, fontFamily: 'MaterialIcons'),
  'view_day': IconData(0xe6b2, fontFamily: 'MaterialIcons'),
  'view_headline': IconData(0xe6b3, fontFamily: 'MaterialIcons'),
  'view_in_ar': IconData(0xe6b4, fontFamily: 'MaterialIcons'),
  'view_kanban': IconData(0xf059c, fontFamily: 'MaterialIcons'),
  'view_list':
      IconData(0xe6b5, fontFamily: 'MaterialIcons', matchTextDirection: true),
  'view_module': IconData(0xe6b6, fontFamily: 'MaterialIcons'),
  'view_quilt':
      IconData(0xe6b7, fontFamily: 'MaterialIcons', matchTextDirection: true),
  'view_sidebar': IconData(0xe6b8, fontFamily: 'MaterialIcons'),
  'view_stream': IconData(0xe6b9, fontFamily: 'MaterialIcons'),
  'view_timeline': IconData(0xf059d, fontFamily: 'MaterialIcons'),
  'view_week': IconData(0xe6ba, fontFamily: 'MaterialIcons'),
  'vignette': IconData(0xe6bb, fontFamily: 'MaterialIcons'),
  'villa': IconData(0xe6bc, fontFamily: 'MaterialIcons'),
  'visibility': IconData(0xe6bd, fontFamily: 'MaterialIcons'),
  'visibility_off': IconData(0xe6be, fontFamily: 'MaterialIcons'),
  'voice_chat': IconData(0xe6bf, fontFamily: 'MaterialIcons'),
  'voice_over_off': IconData(0xe6c0, fontFamily: 'MaterialIcons'),
  'voicemail': IconData(0xe6c1, fontFamily: 'MaterialIcons'),
  'volcano': IconData(0xf07d3, fontFamily: 'MaterialIcons'),
  'volume_down': IconData(0xe6c2, fontFamily: 'MaterialIcons'),
  'volume_down_alt': IconData(0xf059e, fontFamily: 'MaterialIcons'),
  'volume_mute': IconData(0xe6c3, fontFamily: 'MaterialIcons'),
  'volume_off': IconData(0xe6c4, fontFamily: 'MaterialIcons'),
  'volume_up': IconData(0xe6c5, fontFamily: 'MaterialIcons'),
  'volunteer_activism': IconData(0xe6c6, fontFamily: 'MaterialIcons'),
  'vpn_key': IconData(0xe6c7, fontFamily: 'MaterialIcons'),
  'vpn_key_off': IconData(0xf059f, fontFamily: 'MaterialIcons'),
  'vpn_lock': IconData(0xe6c8, fontFamily: 'MaterialIcons'),
  'vrpano': IconData(0xe6c9, fontFamily: 'MaterialIcons'),
  'wallet': IconData(0xf07d4, fontFamily: 'MaterialIcons'),
  'wallet_giftcard': IconData(0xe13e, fontFamily: 'MaterialIcons'),
  'wallet_membership': IconData(0xe13f, fontFamily: 'MaterialIcons'),
  'wallet_travel': IconData(0xe140, fontFamily: 'MaterialIcons'),
  'wallpaper': IconData(0xe6ca, fontFamily: 'MaterialIcons'),
  'warehouse': IconData(0xf05a0, fontFamily: 'MaterialIcons'),
  'warning': IconData(0xe6cb, fontFamily: 'MaterialIcons'),
  'warning_amber': IconData(0xe6cc, fontFamily: 'MaterialIcons'),
  'wash': IconData(0xe6cd, fontFamily: 'MaterialIcons'),
  'watch': IconData(0xe6ce, fontFamily: 'MaterialIcons'),
  'watch_later': IconData(0xe6cf, fontFamily: 'MaterialIcons'),
  'watch_off': IconData(0xf05a1, fontFamily: 'MaterialIcons'),
  'water': IconData(0xe6d0, fontFamily: 'MaterialIcons'),
  'water_damage': IconData(0xe6d1, fontFamily: 'MaterialIcons'),
  'water_drop': IconData(0xf05a2, fontFamily: 'MaterialIcons'),
  'waterfall_chart': IconData(0xe6d2, fontFamily: 'MaterialIcons'),
  'waves': IconData(0xe6d3, fontFamily: 'MaterialIcons'),
  'waving_hand': IconData(0xf05a3, fontFamily: 'MaterialIcons'),
  'wb_auto': IconData(0xe6d4, fontFamily: 'MaterialIcons'),
  'wb_cloudy': IconData(0xe6d5, fontFamily: 'MaterialIcons'),
  'wb_incandescent': IconData(0xe6d6, fontFamily: 'MaterialIcons'),
  'wb_iridescent': IconData(0xe6d7, fontFamily: 'MaterialIcons'),
  'wb_shade': IconData(0xe6d8, fontFamily: 'MaterialIcons'),
  'wb_sunny': IconData(0xe6d9, fontFamily: 'MaterialIcons'),
  'wb_twighlight': IconData(0xe6da, fontFamily: 'MaterialIcons'),
  'wb_twilight': IconData(0xe6db, fontFamily: 'MaterialIcons'),
  'wc': IconData(0xe6dc, fontFamily: 'MaterialIcons'),
  'web': IconData(0xe6dd, fontFamily: 'MaterialIcons'),
  'web_asset': IconData(0xe6de, fontFamily: 'MaterialIcons'),
  'web_asset_off': IconData(0xe6df, fontFamily: 'MaterialIcons'),
  'web_stories': IconData(0xe6e0, fontFamily: 'MaterialIcons'),
  'webhook': IconData(0xf05a4, fontFamily: 'MaterialIcons'),
  'wechat': IconData(0xf05a5, fontFamily: 'MaterialIcons'),
  'weekend': IconData(0xe6e1, fontFamily: 'MaterialIcons'),
  'west': IconData(0xe6e2, fontFamily: 'MaterialIcons'),
  'whatshot': IconData(0xe6e3, fontFamily: 'MaterialIcons'),
  'wheelchair_pickup': IconData(0xe6e4, fontFamily: 'MaterialIcons'),
  'where_to_vote': IconData(0xe6e5, fontFamily: 'MaterialIcons'),
  'widgets': IconData(0xe6e6, fontFamily: 'MaterialIcons'),
  'width_full': IconData(0xf07d5, fontFamily: 'MaterialIcons'),
  'width_normal': IconData(0xf07d6, fontFamily: 'MaterialIcons'),
  'width_wide': IconData(0xf07d7, fontFamily: 'MaterialIcons'),
  'wifi': IconData(0xe6e7, fontFamily: 'MaterialIcons'),
  'wifi_1_bar': IconData(0xf07d8, fontFamily: 'MaterialIcons'),
  'wifi_2_bar': IconData(0xf07d9, fontFamily: 'MaterialIcons'),
  'wifi_calling': IconData(0xe6e8, fontFamily: 'MaterialIcons'),
  'wifi_calling_3': IconData(0xe6e9, fontFamily: 'MaterialIcons'),
  'wifi_channel': IconData(0xf05a7, fontFamily: 'MaterialIcons'),
  'wifi_find': IconData(0xf05a8, fontFamily: 'MaterialIcons'),
  'wifi_lock': IconData(0xe6ea, fontFamily: 'MaterialIcons'),
  'wifi_off': IconData(0xe6eb, fontFamily: 'MaterialIcons'),
  'wifi_password': IconData(0xf05a9, fontFamily: 'MaterialIcons'),
  'wifi_protected_setup': IconData(0xe6ec, fontFamily: 'MaterialIcons'),
  'wifi_tethering': IconData(0xe6ed, fontFamily: 'MaterialIcons'),
  'wifi_tethering_error': IconData(0xf05aa, fontFamily: 'MaterialIcons'),
  'wifi_tethering_off': IconData(0xe6ef, fontFamily: 'MaterialIcons'),
  'wind_power': IconData(0xf07da, fontFamily: 'MaterialIcons'),
  'window': IconData(0xe6f0, fontFamily: 'MaterialIcons'),
  'wine_bar': IconData(0xe6f1, fontFamily: 'MaterialIcons'),
  'woman': IconData(0xf05ab, fontFamily: 'MaterialIcons'),
  'woman_2': IconData(0xf087c, fontFamily: 'MaterialIcons'),
  'woo_commerce': IconData(0xf05ac, fontFamily: 'MaterialIcons'),
  'wordpress': IconData(0xf05ad, fontFamily: 'MaterialIcons'),
  'work': IconData(0xe6f2, fontFamily: 'MaterialIcons'),
  'work_history': IconData(0xf07db, fontFamily: 'MaterialIcons'),
  'work_off': IconData(0xe6f3, fontFamily: 'MaterialIcons'),
  'work_outline': IconData(0xe6f4, fontFamily: 'MaterialIcons'),
  'workspace_premium': IconData(0xf05ae, fontFamily: 'MaterialIcons'),
  'workspaces': IconData(0xe6f5, fontFamily: 'MaterialIcons'),
  'workspaces_filled': IconData(0xe6f6, fontFamily: 'MaterialIcons'),
  'workspaces_outline': IconData(0xe6f7, fontFamily: 'MaterialIcons'),
  'wrap_text':
      IconData(0xe6f8, fontFamily: 'MaterialIcons', matchTextDirection: true),
  'wrong_location': IconData(0xe6f9, fontFamily: 'MaterialIcons'),
  'wysiwyg': IconData(0xe6fa, fontFamily: 'MaterialIcons'),
  'yard': IconData(0xe6fb, fontFamily: 'MaterialIcons'),
  'youtube_searched_for': IconData(0xe6fc, fontFamily: 'MaterialIcons'),
  'zoom_in': IconData(0xe6fd, fontFamily: 'MaterialIcons'),
  'zoom_in_map': IconData(0xf05af, fontFamily: 'MaterialIcons'),
  'zoom_out': IconData(0xe6fe, fontFamily: 'MaterialIcons'),
  'zoom_out_map': IconData(0xe6ff, fontFamily: 'MaterialIcons'),
};
