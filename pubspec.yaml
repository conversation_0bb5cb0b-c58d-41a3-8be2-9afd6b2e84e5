name: thingsboard_app
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: "none" # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.7.2

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  thingsboard_client: ^1.2.0
  intl: ^0.19.0
  flutter_secure_storage: ^9.0.0
  flutter_speed_dial: ^7.0.0
  cupertino_icons: ^1.0.6
  fluro: ^2.0.5
  flutter_svg: ^2.0.9
  jovial_svg: ^1.1.19
  auto_size_text: ^3.0.0-nullsafety.0
  infinite_scroll_pagination: ^4.0.0
  fading_edge_scrollview: ^4.0.0
  stream_transform: ^2.1.0
  flutter_inappwebview: ^5.8.0
  #  flutter_downloader: ^1.6.0
  #  permission_handler: ^8.0.0+2
  #  path_provider: ^2.0.2
  url_launcher: ^6.2.1
  image_picker: ^1.0.4
  mime: ^1.0.4
  logger: ^2.0.2+1
  qr_code_scanner: ^1.0.1
  device_info_plus: ^9.1.1
  geolocator: ^10.1.0
  material_design_icons_flutter: ^7.0.7296
  package_info_plus: ^5.0.1
  dart_jsonwebtoken: ^2.12.1
  crypto: ^3.0.3
  flutter_form_builder: ^9.1.1
  form_builder_validators: ^11.1.2
  flutter_html: ^3.0.0-beta.2
  universal_html: ^2.2.4
  universal_platform: ^1.0.0+1
  preload_page_view: ^0.2.0
  flutter_localizations:
    sdk: flutter
  firebase_core: ^2.24.2
  firebase_messaging: ^14.7.10
  flutter_local_notifications: ^16.3.0
  flutter_app_badger: ^1.5.0
  timeago: ^3.6.1
  # flutter_slidable: ^2.0.0  # Temporarily disabled due to compatibility issues
  flutter_bloc: ^8.1.5
  get_it: ^7.6.7
  equatable: ^2.0.5
  uni_links: ^0.5.1
  hive: ^2.2.3
  hive_flutter: ^1.1.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_launcher_icons: ^0.13.1
  mocktail: ^1.0.3
  bloc_test: ^9.1.7
  hive_generator: ^2.0.1
  build_runner: ^2.4.9

flutter:
  uses-material-design: true
  assets:
    - assets/images/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware.

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages

flutter_icons:
  android: "launcher_icon"
  ios: true
  remove_alpha_ios: true
  image_path: "assets/images/thingsboard.png"
flutter_intl:
  enabled: true
